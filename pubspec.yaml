name: luxor_admin
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0-78.0.dev

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  get: ^4.6.6
  cloud_firestore: ^5.4.3
  cloud_functions: ^5.1.3
  firebase_auth: ^5.3.1
  firebase_storage: ^12.3.2
  firebase_core: ^3.6.0
  # firebase_ui_auth: ^1.12.0  # Temporarily commented out due to compilation issues
  get_storage: ^2.1.1
  google_fonts: ^6.2.1
  intl: ^0.20.2
  go_router: ^16.3.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_date_range_picker: ^0.2.1
  image_picker: ^1.1.2
  file_picker: ^9.2.3
  flutter_image_compress: ^2.3.0
  flutter_widget_from_html: ^0.17.1
  advanced_chips_input: ^0.1.5
  webview_flutter: ^4.2.1
  textfield_tags: ^3.0.1
  responsive_grid_list: ^1.4.0
  multi_dropdown: ^3.0.1
  html_editor_enhanced:
    git:
      url: https://github.com/MetaDC/html-editor-enhanced.git
      ref: master
  webviewx_plus: ^0.5.0
  markdown: ^7.2.2
  flutter_markdown: ^0.7.1
  carousel_slider: ^5.1.1
  flutter_bloc: ^9.1.1
  bloc: ^9.1.0
  dio: ^5.4.3+1
  collection: ^1.18.0
  shared_preferences: ^2.2.2
  flutter_svg: ^2.0.10+1
  fluttertoast: ^9.0.0
  flutter_screenutil: ^5.9.0
  another_flutter_splash_screen: ^1.2.0
  cached_network_image: ^3.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/
    # - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
# ScreenUtilInit(
#       designSize: const Size(360, 800),
#       minTextAdapt: true,
#       splitScreenMode: true,
#       builder: (_, child) {
#         return RepositoryProvider(
#           create: (context) => UserRepository(),
#           child: MaterialApp(
#             debugShowCheckedModeBanner: false,
#             theme: ThemeData(
#               primarySwatch: Colors.blue,
#               fontFamily: GoogleFonts.poppins().fontFamily,
#             ),
#             initialRoute: FirebaseAuth.instance.currentUser == null
#                 ? RouteScreens.login.name
#                 : RouteScreens.dashboard.name,
#             // onGenerateRoute: NavigationRoutesGenerator.onGenerateRoute,
#             scrollBehavior: MyCustomScrollBehavior(),
#           ),
#         );
#       },
#     );
