/* import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:luxor_admin/models/blog_model.dart';
import 'package:luxor_admin/models/generic_response_model.dart';
import 'package:luxor_admin/modules/addEditBlog/image_picker.dart';
import 'package:luxor_admin/modules/addEditBlog/repositories/add_edit_blog_repository.dart';
import 'package:flutter/material.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';
import 'package:textfield_tags/textfield_tags.dart';
part 'add_edit_blog_event.dart';
part 'add_edit_blog_state.dart';

class AddEditBlogBloc extends Bloc<AddEditBlogEvent, AddEditBlogState> {
  final AddEditBlogRepository addEditBlogRepository;
  final ImagePickerService imagePickerService;

  TextEditingController metaTitleTextEditingController =
      TextEditingController();
  TextEditingController markdownTextEditingController = TextEditingController();
  TextEditingController permaLinkTextEditingController =
      TextEditingController();
  TextEditingController thumbnailTextEditingController =
      TextEditingController();

  final List<String> initialTags = [];
  final StringTagController tagsController = StringTagController();

  List<String> uploadedImages = [];

  final ValueNotifier<String> markdownNotifier = ValueNotifier<String>("");

  MultiSelectController otherBlogsSelectController = MultiSelectController();

  List<BlogModel> allBlogs = [];

  List<BlogModel> otherBlogs = [];

  AddEditBlogBloc({
    required this.addEditBlogRepository,
    required this.imagePickerService,
  }) : super(AddEditBlogInitial()) {
    on<AddEditBlogEvent>((event, emit) {});

    on<AddBlogEvent>(_addBlogEvent);

    on<UploadImagesEvent>(_uploadImagesEvent);
  }

  FutureOr<void> _addBlogEvent(
    AddBlogEvent event,
    Emitter<AddEditBlogState> emit,
  ) async {
    emit(AddingBlogState());

    event.blogModel.imagesLinks = uploadedImages;

    if (otherBlogsSelectController.selectedOptions.isNotEmpty) {
      for (var selectedOption in otherBlogsSelectController.selectedOptions) {
        BlogModel? foundBlogModel = allBlogs.firstWhereOrNull(
          (BlogModel blogModel) => blogModel.id == selectedOption.value,
        );

        if (foundBlogModel != null) {
          if (event.blogModel.otherBlogs == null) {
            event.blogModel.otherBlogs = [
              BlogModel(
                id: foundBlogModel.id,
                metaTitle: foundBlogModel.metaTitle,
                thumbnail: foundBlogModel.thumbnail,
              ),
            ];
          } else {
            event.blogModel.otherBlogs?.add(
              BlogModel(
                id: foundBlogModel.id,
                metaTitle: foundBlogModel.metaTitle,
                thumbnail: foundBlogModel.thumbnail,
              ),
            );
          }
        }
      }
    } else {
      event.blogModel.otherBlogs = [];
    }
    await addEditBlogRepository.addBlog(blogModel: event.blogModel);

    emit(BlogAddedState());
  }

  FutureOr<void> _uploadImagesEvent(
    UploadImagesEvent event,
    Emitter<AddEditBlogState> emit,
  ) async {
    try {
      emit(UploadingImagesState());

      List<String> imagesUrls = await addEditBlogRepository.uploadImages(
        imagesToUpload: event.imagesToUpload,
      );

      uploadedImages.addAll(imagesUrls);

      emit(ImagesUploadedState());
    } catch (e) {
      print(e);
    }
  }

  @override
  Future<void> close() {
    metaTitleTextEditingController.dispose();
    markdownTextEditingController.dispose();
    permaLinkTextEditingController.dispose();
    thumbnailTextEditingController.dispose();
    return super.close();
  }
}
 */

import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:luxor_admin/models/blog_model.dart';
import 'package:luxor_admin/modules/addEditBlog/image_picker.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:textfield_tags/textfield_tags.dart';
import '../../../models/generic_response_model.dart';
import '../repositories/add_edit_blog_repository.dart';
part 'add_edit_blog_event.dart';
part 'add_edit_blog_state.dart';

class AddEditBlogBloc extends Bloc<AddEditBlogEvent, AddEditBlogState> {
  final AddEditBlogRepository addEditBlogRepository;
  final ImagePickerService imagePickerService;
  TextEditingController metaTitleTextEditingController =
      TextEditingController();
  TextEditingController markdownTextEditingController = TextEditingController();
  TextEditingController permaLinkTextEditingController =
      TextEditingController();
  TextEditingController thumbnailTextEditingController =
      TextEditingController();

  final List<String> initialTags = [];
  final StringTagController tagsController = StringTagController();

  List<String> uploadedImages = [];

  final ValueNotifier<String> markdownNotifier = ValueNotifier<String>("");

  MultiSelectController<String> multiDropdownController =
      MultiSelectController<String>();

  List<BlogModel> allBlogs = [];

  List<BlogModel> otherBlogs = [];

  AddEditBlogBloc({
    required this.addEditBlogRepository,
    required this.imagePickerService,
  }) : super(AddEditBlogInitial()) {
    on<AddEditBlogEvent>((event, emit) {});

    on<AddBlogEvent>(_addBlogEvent);

    on<UploadImagesEvent>(_uploadImagesEvent);
  }

  FutureOr<void> _addBlogEvent(
    AddBlogEvent event,
    Emitter<AddEditBlogState> emit,
  ) async {
    emit(AddingBlogState());

    event.blogModel.imagesLinks = uploadedImages;

    // Use multiDropdownController instead of otherBlogsSelectController
    if (multiDropdownController.selectedItems.isNotEmpty) {
      event.blogModel.otherBlogs = [];
      for (var selectedItem in multiDropdownController.selectedItems) {
        BlogModel? foundBlogModel = allBlogs.firstWhereOrNull(
          (BlogModel blogModel) =>
              blogModel.id.toString() == selectedItem.value,
        );

        event.blogModel.otherBlogs?.add(
          BlogModel(
            id: foundBlogModel?.id,
            metaTitle: foundBlogModel?.metaTitle,
            thumbnail: foundBlogModel?.thumbnail,
          ),
        );
      }
    } else {
      event.blogModel.otherBlogs = [];
    }
    await addEditBlogRepository.addBlog(blogModel: event.blogModel);

    emit(BlogAddedState());
  }

  FutureOr<void> _uploadImagesEvent(
    UploadImagesEvent event,
    Emitter<AddEditBlogState> emit,
  ) async {
    try {
      emit(UploadingImagesState());

      List<String> imagesUrls = await addEditBlogRepository.uploadImages(
        imagesToUpload: event.imagesToUpload,
      );

      uploadedImages.addAll(imagesUrls);

      emit(ImagesUploadedState());
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Future<void> close() {
    metaTitleTextEditingController.dispose();
    markdownTextEditingController.dispose();
    permaLinkTextEditingController.dispose();
    thumbnailTextEditingController.dispose();
    return super.close();
  }
}
