import 'package:luxor_admin/configurations/app_configurations.dart';
import 'package:luxor_admin/widgets/height_box.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/src/widgets/text.dart' as TextWidget;

class MarkdownTips extends StatelessWidget {
  const MarkdownTips({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 90.sp,
        vertical: 20.sp,
      ),
      color: colorsConstants.whiteColor,
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 5.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Image.asset(assetsConstants.headingTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.paragraphTips),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget.Text(
                      "Note: To change font style in paragraph use inline styling like shown in below example",
                      style: TextStyle(
                          fontSize: 5.sp, fontWeight: FontWeight.bold),
                    ),
                    TextWidget.Text(
                      "<p style=\"font-size:20; color:blue\">Google’s Classroom app</p>",
                      style: TextStyle(
                          fontSize: 5.sp,
                          backgroundColor: colorsConstants.slateGrey),
                    ),
                  ],
                ),
              ),
              HeightBox(5.h),
              Image.asset(assetsConstants.boldTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.italicTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.boldItalicTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.orderedListTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.unorderedListTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.startingUlItemWithNumberTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.linkTip),
              HeightBox(5.h),
              Image.asset(assetsConstants.linkTargetTip),
              HeightBox(5.h),
              Image.asset(assetsConstants.imagesTip),
              HeightBox(5.h),
              Image.asset(assetsConstants.imageSizeTip),
              HeightBox(5.h),
              Image.asset(assetsConstants.blockquotesTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.nestedBlockquotesTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.blockquotesMultiParagraphTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.blockquotesWithOtherElementTips),
              HeightBox(5.h),
              Image.asset(assetsConstants.admonitionsTip),
              HeightBox(20.h),
            ],
          ),
        ),
      ),
    );
  }
}
