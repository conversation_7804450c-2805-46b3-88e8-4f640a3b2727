import 'package:luxor_admin/firebase.dart';
import 'package:luxor_admin/models/blog_model.dart';
import 'package:luxor_admin/routers/extensions.dart';
import 'package:luxor_admin/routers/navigation_helper.dart';
import 'package:luxor_admin/widgets/primary_button.dart';
import 'package:luxor_admin/widgets/width_box.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luxor_admin/configurations/app_configurations.dart';
import 'package:luxor_admin/modules/dashboard/bloc/dashboard_bloc.dart';
import 'package:luxor_admin/modules/dashboard/repositories/dashboard_repository.dart';
import 'package:luxor_admin/utils/utils.dart';
import 'package:luxor_admin/widgets/height_box.dart';
import 'package:responsive_grid_list/responsive_grid_list.dart';
import '../../../gallery_sec.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  int currentPage = 0;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          DashboardBloc(dashboardRepository: DashboardRepository())
            ..add(LoadDashboardData()),
      child: Builder(
        builder: (context) {
          DashboardBloc dashboardBloc = context.read<DashboardBloc>();

          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              elevation: 5,
              shadowColor: Colors.black12,
              toolbarHeight: 16.sp,
              leadingWidth: 30.sp,
              backgroundColor: Colors.blue,
              leading: Padding(
                padding: const EdgeInsets.only(left: 22, top: 8, bottom: 8),
                child: Image.asset(
                  "assets/images/Luxor_logo.png",
                  fit: BoxFit.contain,
                  color: Colors.white,
                ),
              ),
              actions: [
                PrimaryButton(
                  onTap: () {
                    moveToAddEditBlogScreen(
                      context: context,
                      allBlogs: dashboardBloc.state.allBlogs,
                      revertCallback: (_) {
                        dashboardBloc.add(LoadDashboardData());
                      },
                    );
                  },
                  title: "Add Blog",
                  backgroundColor: Colors.white,
                  titleStyle: TextStyle(fontSize: 3.sp, color: Colors.black),
                  width: 20.w,
                  margin: EdgeInsets.symmetric(vertical: 4.sp),
                  padding: EdgeInsets.symmetric(vertical: 1.sp),
                ),
                WidthBox(5.w),
                InkWell(
                  onTap: () {
                    FirebaseAuth.instance.signOut();
                    moveToLoginScreen(
                        context: context,
                        operation: NavigationOperations.pushReplacementNamed);
                  },
                  child: const Icon(
                    Icons.logout_rounded,
                    color: Colors.white,
                  ),
                ),
                WidthBox(5.w),
              ],
            ),
            body: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  width: 140,
                  color: Colors.blue,
                  child: Column(
                    children: [
                      const SizedBox(height: 10),
                      // Text("Luxor"),
                      ElevatedButton(
                          style: const ButtonStyle(
                              padding: WidgetStatePropertyAll(
                                  EdgeInsets.only(right: 30, left: 30)),
                              elevation: WidgetStatePropertyAll(1),
                              shape: WidgetStatePropertyAll(
                                  ContinuousRectangleBorder()),
                              backgroundColor:
                                  WidgetStatePropertyAll(Colors.white)),
                          onPressed: () {
                            setState(() {
                              currentPage = 0;
                            });
                          },
                          child: const Text(
                            "Blogs",
                            style: TextStyle(color: Colors.black, fontSize: 15),
                          )),
                      const SizedBox(height: 10),
                      ElevatedButton(
                          style: const ButtonStyle(
                              elevation: WidgetStatePropertyAll(1),
                              shape: WidgetStatePropertyAll(
                                  ContinuousRectangleBorder()),
                              backgroundColor:
                                  WidgetStatePropertyAll(Colors.white)),
                          onPressed: () {
                            setState(() {
                              currentPage = 1;
                            });
                          },
                          child: const Text(
                            "Gallery",
                            style: TextStyle(color: Colors.black, fontSize: 15),
                          ))
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      // mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        [
                          BlogsSection(dashboardBloc: dashboardBloc),
                          const GalarySection()
                        ][currentPage],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class BlogsSection extends StatefulWidget {
  const BlogsSection({
    super.key,
    required this.dashboardBloc,
  });

  final DashboardBloc dashboardBloc;

  @override
  State<BlogsSection> createState() => _BlogsSectionState();
}

class _BlogsSectionState extends State<BlogsSection> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DashboardBloc, DashboardState>(
      listener: (context, state) {
        if (state is DashboardErrorState) {
          Utils.showSnackbar(
              context: context, message: state.errorData?.data["message"]);
        } else if (state is BlogDeletedState) {
          Navigator.pop(context);
          context.read<DashboardBloc>().add(LoadDashboardData());
        }
      },
      builder: (context, state) {
        if (state is LoadingDashboardDataState) {
          return const Center(
            child: CircularProgressIndicator(
              color: Colors.blue,
            ),
          );
        }

        if (state is DashboardDataLoadedState && state.allBlogs.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "No data found",
                  style: TextStyle(fontSize: 5.sp),
                ),
                PrimaryButton(
                  onTap: () {
                    moveToAddEditBlogScreen(
                      context: context,
                      allBlogs: widget.dashboardBloc.state.allBlogs,
                      revertCallback: (_) {
                        widget.dashboardBloc.add(LoadDashboardData());
                      },
                    );
                  },
                  title: "Add Blog",
                  titleStyle: TextStyle(fontSize: 4.sp, color: Colors.white),
                  width: 24.w,
                  margin: EdgeInsets.symmetric(vertical: 5.sp),
                  padding: EdgeInsets.symmetric(vertical: 2.sp),
                ),
              ],
            ),
          );
        }

        return ResponsiveGridList(
          horizontalGridSpacing: 8.sp,
          verticalGridSpacing: 8.sp,
          horizontalGridMargin: 10.sp,
          verticalGridMargin: 10.sp,
          minItemWidth: 20.sp,
          minItemsPerRow: 2,
          maxItemsPerRow: 5,
          listViewBuilderOptions: ListViewBuilderOptions(
              shrinkWrap:
                  true), // Options that are getting passed to the ListView.builder() function
          children: state.allBlogs.map((BlogModel blogModel) {
            return Card(
              elevation: 2,
              clipBehavior: Clip.antiAlias,
              color: Colors.white,
              surfaceTintColor: Colors.white,
              margin: EdgeInsets.zero,
              child: Stack(
                children: [
                  InkWell(
                    onTap: () {
                      moveToAddEditBlogScreen(
                        context: context,
                        blogModel: blogModel,
                        allBlogs: widget.dashboardBloc.state.allBlogs,
                        revertCallback: (_) {
                          widget.dashboardBloc.add(LoadDashboardData());
                        },
                      );
                    },
                    onLongPress: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return DeletePopup(
                            dashboardBloc: widget.dashboardBloc,
                            blogModel: blogModel,
                          );
                        },
                      );
                    },
                    hoverColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Stack(
                          children: [
                            Image.network(
                              blogModel.thumbnail ?? "",
                              height: 50.sp,
                              width: 70.sp,
                              fit: BoxFit.cover,
                              // errorBuilder:
                              //     (context, error, stackTrace) {
                              //   return Center(
                              //     child: Icon(Icons.error),
                              //   );
                              // },
                            ),
                            Container(
                              height: 50.sp,
                              width: 70.sp,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.black.withOpacity(0.3),
                                    Colors.black.withOpacity(0.0)
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                        HeightBox(2.sp),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 2.w),
                          child: Text(
                            blogModel.metaTitle ?? "",
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(fontSize: 3.sp),
                          ),
                        ),
                        HeightBox(2.sp),
                      ],
                    ),
                  ),
                  Positioned(
                    left: 0.99.sp,
                    top: 1.sp,
                    child: InkWell(
                      onTap: () async {
                        print('====${blogModel.id}');
                        await FBFireStore.blogs
                            .doc(blogModel.id)
                            .update({'isFav': !(blogModel.isFav ?? false)});
                      },
                      child: Icon(
                        blogModel.isFav == true
                            ? Icons.star
                            : Icons.star_border_outlined,
                        color: Colors.white,
                        size: 5.sp,
                      ),
                    ),
                  ),
                  Positioned(
                    right: 1.5.sp,
                    top: 1.sp,
                    child: InkWell(
                      onTap: () {
                        widget.dashboardBloc.add(
                          ShowHideBlogEvent(
                            blogModel: blogModel,
                          ),
                        );
                      },
                      child: Icon(
                        blogModel.isVisible == true
                            ? Icons.visibility_rounded
                            : Icons.visibility_off_rounded,
                        color: Colors.white,
                        size: 5.sp,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(), // The list of widgets in the list
        );
      },
    );
  }
}

class DeletePopup extends StatelessWidget {
  const DeletePopup({
    super.key,
    required this.dashboardBloc,
    required this.blogModel,
  });

  final DashboardBloc dashboardBloc;
  final BlogModel blogModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      alignment: Alignment.center,
      margin: EdgeInsets.symmetric(
        horizontal: 90.sp,
        vertical: 60.sp,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Are you sure you want to delete?",
            style: TextStyle(fontSize: 10.sp),
          ),
          HeightBox(10.sp),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              PrimaryButton(
                title: "Delete",
                width: 30.w,
                height: 40.h,
                backgroundColor: colorsConstants.errorRed,
                onTap: () async {
                  dashboardBloc.add(
                    DeleteBlogEvent(
                      blogModel: blogModel,
                    ),
                  );
                },
              ),
              WidthBox(2.sp),
              PrimaryButton(
                title: "Cancel",
                width: 30.w,
                height: 40.h,
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            ],
          )
        ],
      ),
    );
  }
}
