part of 'dashboard_bloc.dart';

class DashboardState {
  ApiResponse? errorData;
  List<BlogModel> allBlogs = [];
}

final class DashboardInitial extends DashboardState {}

final class LoadingDashboardDataState extends DashboardState {}

final class DashboardDataLoadedState extends DashboardState {
  DashboardDataLoadedState({required List<BlogModel> blogs}) {
    super.allBlogs = blogs;
  }
}

final class BlogDeletedState extends DashboardState {
  BlogDeletedState();
}

final class DashboardErrorState extends DashboardState {
  DashboardErrorState({required ApiResponse errorData}) {
    this.errorData = errorData;
  }
}
