import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:luxor_admin/models/blog_model.dart';
import 'package:luxor_admin/models/generic_response_model.dart';
import 'package:luxor_admin/modules/dashboard/repositories/dashboard_repository.dart';
import 'package:flutter/material.dart';

part 'dashboard_event.dart';
part 'dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  DashboardRepository dashboardRepository;

  DashboardBloc({required this.dashboardRepository})
      : super(DashboardInitial()) {
    on<DashboardEvent>((event, emit) {});
    on<LoadDashboardData>(_onLoadDashboardDataEvent);
    on<DeleteBlogEvent>(_onDeleteBlogEvent);
    on<ShowHideBlogEvent>(_onShowHideBlogEvent);
  }

  FutureOr<void> _onLoadDashboardDataEvent(
      DashboardEvent event, Emitter<DashboardState> emit) async {
    List<BlogModel> blogs = await dashboardRepository.fetchBlogs();

    if (blogs.isNotEmpty) {
      emit(DashboardDataLoadedState(blogs: blogs));
    } else {
      // emit(DashboardErrorState(errorData: apiResponse));
    }
  }

  FutureOr<void> _onDeleteBlogEvent(
      DeleteBlogEvent event, Emitter<DashboardState> emit) async {
    await dashboardRepository.deleteBlog(blogModel: event.blogModel);
    emit(BlogDeletedState());
  }

  FutureOr<void> _onShowHideBlogEvent(
      ShowHideBlogEvent event, Emitter<DashboardState> emit) async {
    state.allBlogs.firstWhereOrNull((blog) {
      if (blog.id == event.blogModel.id) {
        debugPrint("Data");
      }
      return blog.id == event.blogModel.id;
    })?.isVisible = !event.blogModel.isVisible!;

    emit(DashboardDataLoadedState(blogs: state.allBlogs));

    // List<BlogModel> blogs =
    //     await
    dashboardRepository.showHideBlog(blogModel: event.blogModel);

    // emit(DashboardDataLoadedState(blogs: blogs));
  }
}
