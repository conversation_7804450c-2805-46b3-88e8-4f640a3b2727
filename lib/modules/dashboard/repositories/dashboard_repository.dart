import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:luxor_admin/models/blog_model.dart';

class DashboardRepository {
  Future<List<BlogModel>> fetchBlogs() async {
    QuerySnapshot<Map<String, dynamic>> allBlogsRefs =
        await FirebaseFirestore.instance.collection("Blogs").get();

    List<BlogModel> blogs = [];

    for (var blogDoc in allBlogsRefs.docs) {
      final data = blogDoc.data();
      BlogModel blogModel = BlogModel.fromMap(blogDoc.data())..id = blogDoc.id;
      blogs.add(blogModel);
    }

    return blogs;
  }

  Future<void> deleteBlog({required BlogModel blogModel}) async {
    await FirebaseFirestore.instance
        .collection("Blogs")
        .doc(blogModel.id)
        .delete();
  }

  Future<List<BlogModel>> showHideBlog({required BlogModel blogModel}) async {
    // blogModel.isVisible = !blogModel.isVisible!;
    await FirebaseFirestore.instance
        .collection("Blogs")
        .doc(blogModel.id)
        .update(blogModel.toMap());

    return await fetchBlogs();
  }
}
