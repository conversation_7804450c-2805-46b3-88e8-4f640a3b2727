import 'package:luxor_admin/routers/extensions.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:luxor_admin/modules/login/bloc/login_bloc.dart';
import 'package:luxor_admin/modules/login/repositories/login_repository.dart';
import 'package:luxor_admin/routers/navigation_helper.dart';
import 'package:luxor_admin/utils/utils.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    // final providers = [EmailAuthProvider()];
    // return Scaffold(
    //   body: SignInScreen(
    //     providers: providers,
    //     showAuthActionSwitch: false,
    //     actions: [
    //       AuthStateChangeAction(
    //         (context, state) {
    //           if (state.user != null) context.go(Routes.orders);
    //         },
    //       )
    //     ],
    //   ),
    // );

    return BlocProvider(
      create: (context) => LoginBloc(loginRepository: LoginRepository()),
      child: BlocConsumer<LoginBloc, LoginState>(
        listener: (context, state) async {
          if (state is LoginSuccessState) {
            moveToDashboardScreen(
                context: context,
                operation: NavigationOperations.pushReplacementNamed);
          } else if (state is LoginErrorState) {
            Utils.showSnackbar(
              type: SnackBarType.error,
              context: context,
              message: state.errorData?.data["message"],
            );
          }
        },
        builder: (context, state) {
          LoginBloc loginBloc = context.read<LoginBloc>();
          // return const Placeholder();

          return SignInScreen(
            providers: [EmailAuthProvider()],
            actions: [
              AuthStateChangeAction<SignedIn>((context, state) {
                Navigator.pushReplacementNamed(context, '/profile');
              }),
              AuthStateChangeAction<SigningUp>((context, state) {
                Navigator.pushReplacementNamed(context, '/profile');
              }),
            ],
          );
        },
      ),
    );
  }
}
