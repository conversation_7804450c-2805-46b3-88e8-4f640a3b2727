import 'package:luxor_admin/configurations/app_configurations.dart';
import 'package:luxor_admin/enums/enums.dart';
import 'package:luxor_admin/models/generic_response_model.dart';

class LoginRepository {
  Future<ApiResponse> signIn({
    required String email,
    required String password,
  }) async {
    ApiResponse apiResponse = await networkManager.callAPI(
      endpoint: apiEndPoints.login,
      requestType: RequestType.post,
      body: {
        "email": email,
        "password": password,
      },
    );

    return apiResponse;
  }
}
