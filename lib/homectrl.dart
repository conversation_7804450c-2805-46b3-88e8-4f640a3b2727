import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:luxor_admin/models/gallerymodel.dart';

import 'firebase.dart';

class Homectrl extends GetxController {
  List<GalleryModel> gallery = [];

  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? galleryStream;

  @override
  void onInit() {
    super.onInit();
    getgalleryData();
  }

  getgalleryData() {
    try {
      galleryStream?.cancel();
      galleryStream = FBFireStore.gallery.snapshots().listen((event) {
        print("-------------------");
        gallery = event.docs.map((e) => GalleryModel.fromSnap(e)).toList();
        print("..............................${gallery.length}");
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
