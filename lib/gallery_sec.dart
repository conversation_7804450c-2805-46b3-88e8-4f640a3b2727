import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:luxor_admin/homectrl.dart';
import 'configurations/app_configurations.dart';
import 'firebase.dart';
import 'methods.dart';
import 'modules/addEditBlog/image_picker.dart';
import 'widgets/height_box.dart';
import 'widgets/primary_button.dart';
import 'widgets/width_box.dart';

class GalarySection extends StatefulWidget {
  const GalarySection({
    super.key,
  });

  @override
  State<GalarySection> createState() => _GalarySectionState();
}

class _GalarySectionState extends State<GalarySection> {
  bool onSubmitLoad = false;

  int? selectedContent;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<Homectrl>(builder: (ctrl) {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(30),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text("Gallery",
                    style: TextStyle(
                      fontSize: 28,
                    )),
                Row(
                  children: [
                    ElevatedButton(
                        style: const ButtonStyle(
                            elevation: WidgetStatePropertyAll(1),
                            shape: WidgetStatePropertyAll(
                                ContinuousRectangleBorder()),
                            backgroundColor:
                                WidgetStatePropertyAll(Colors.green)),
                        onPressed: () => addImageDialog(
                            context, onSubmitLoad, true, ctrl, 0),
                        child: const Text(
                          "Add Image",
                          style: TextStyle(color: Colors.white, fontSize: 15),
                        )),
                    const SizedBox(width: 5),
                    ElevatedButton(
                        style: const ButtonStyle(
                            elevation: WidgetStatePropertyAll(1),
                            shape: WidgetStatePropertyAll(
                                ContinuousRectangleBorder()),
                            backgroundColor:
                                WidgetStatePropertyAll(Colors.green)),
                        onPressed: () => addVideoDialog(
                            context, onSubmitLoad, true, ctrl, 0),
                        child: const Text(
                          "Add Video",
                          style: TextStyle(color: Colors.white, fontSize: 15),
                        ))
                  ],
                )
              ],
            ),
            const SizedBox(height: 30),
            StaggeredGrid.count(crossAxisCount: 5, children: [
              ...List.generate(
                ctrl.gallery.length,
                (index) {
                  return Card(
                    elevation: 2,
                    clipBehavior: Clip.antiAlias,
                    color: Colors.white,
                    surfaceTintColor: Colors.white,
                    margin: const EdgeInsets.only(right: 16, bottom: 16),
                    child: Stack(
                      children: [
                        InkWell(
                          onLongPress: () => showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              actions: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "Are you sure you want to delete?",
                                      style: TextStyle(fontSize: 10.sp),
                                    ),
                                    HeightBox(10.sp),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        PrimaryButton(
                                          title: "Delete",
                                          width: 30.w,
                                          height: 40.h,
                                          backgroundColor:
                                              colorsConstants.errorRed,
                                          onTap: () async {
                                            FBFireStore.gallery
                                                .doc(ctrl.gallery[index].docId)
                                                .delete();
                                            Navigator.pop(context);
                                          },
                                        ),
                                        WidthBox(2.sp),
                                        PrimaryButton(
                                          title: "Cancel",
                                          width: 30.w,
                                          height: 40.h,
                                          onTap: () {
                                            Navigator.pop(context);
                                          },
                                        ),
                                      ],
                                    )
                                  ],
                                )
                              ],
                            ),
                          ),
                          overlayColor:
                              const WidgetStatePropertyAll(Colors.transparent),
                          hoverColor: Colors.transparent,
                          onTap: () => ctrl.gallery[index].hasImage == true
                              ? addImageDialog(
                                  context, onSubmitLoad, false, ctrl, index)
                              : addVideoDialog(
                                  context, onSubmitLoad, false, ctrl, index),
                          child: Column(
                            children: [
                              Stack(
                                children: [
                                  Image.network(
                                    ctrl.gallery[index].hasImage == true
                                        ? ctrl.gallery[index].imageUrl
                                        : ("VIDEO CONTENT"),
                                    height: 50.sp,
                                    width: 70.sp,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Center(
                                        child: Text("VIDEO CONTENT"),
                                      );
                                    },
                                  ),
                                  Container(
                                    height: 50.sp,
                                    width: 70.sp,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Colors.black.withOpacity(0.3),
                                          Colors.black.withOpacity(0.0)
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              HeightBox(2.sp),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 2.w),
                                child: Text(
                                  ctrl.gallery[index].title ?? "",
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(fontSize: 3.sp),
                                ),
                              ),
                              HeightBox(2.sp),
                            ],
                          ),
                        ),
                        Positioned(
                          left: 0.99.sp,
                          top: 1.sp,
                          child: InkWell(
                            overlayColor: const WidgetStatePropertyAll(
                                Colors.transparent),
                            hoverColor: Colors.transparent,
                            onTap: () async {
                              try {
                                if (ctrl.gallery[index].isFav == false) {
                                  await FBFireStore.gallery
                                      .doc(ctrl.gallery[index].docId)
                                      .update({'isFav': true});
                                } else {
                                  await FBFireStore.gallery
                                      .doc(ctrl.gallery[index].docId)
                                      .update({'isFav': false});
                                }
                              } catch (e) {}
                            },
                            child: Icon(
                              ctrl.gallery[index].isFav == true
                                  ? Icons.star
                                  : Icons.star_border_outlined,
                              color: Colors.grey,
                              size: 5.sp,
                            ),
                          ),
                        ),
                        Positioned(
                          right: 1.5.sp,
                          top: 1.sp,
                          child: InkWell(
                            overlayColor: const WidgetStatePropertyAll(
                                Colors.transparent),
                            hoverColor: Colors.transparent,
                            onTap: () async {
                              try {
                                if (ctrl.gallery[index].isVisible == true) {
                                  await FBFireStore.gallery
                                      .doc(ctrl.gallery[index].docId)
                                      .update({'isVisible': false});
                                } else {
                                  await FBFireStore.gallery
                                      .doc(ctrl.gallery[index].docId)
                                      .update({'isVisible': true});
                                }
                              } catch (e) {}
                            },
                            child: Icon(
                              ctrl.gallery[index].isVisible == true
                                  ? Icons.visibility_rounded
                                  : Icons.visibility_off_rounded,
                              color: Colors.grey,
                              size: 5.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              )
            ])
          ],
        ),
      );
    });
  }
}

Future<dynamic> addVideoDialog(BuildContext context, bool onSubmitLoad,
    bool isNew, Homectrl ctrl, int index) {
  TextEditingController videotitlectrl = TextEditingController();
  TextEditingController videodesctrl = TextEditingController();
  TextEditingController videothumbnailctrl = TextEditingController();
  TextEditingController videoUrlctrl = TextEditingController();

  bool videoIsChecked = false;

  if (!isNew) {
    videotitlectrl.text = ctrl.gallery[index].title;
    videodesctrl.text = ctrl.gallery[index].desc;
    videoUrlctrl.text = ctrl.gallery[index].imageUrl;
    videothumbnailctrl.text = ctrl.gallery[index].thumbnail!;
  }

  return showDialog(
    context: context,
    builder: (context) => StatefulBuilder(builder: (context, setState2) {
      return AlertDialog(
        backgroundColor: Colors.white,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text("Add Video"),
            IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const Icon(Icons.close),
            )
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 300,
          child: Column(
            children: [
              TextFormField(
                controller: videotitlectrl,
                decoration: const InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                        borderRadius: BorderRadius.all(Radius.circular(10))),
                    labelText: 'Title'),
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: videodesctrl,
                decoration: const InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                        borderRadius: BorderRadius.all(Radius.circular(10))),
                    labelText: 'Description'),
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: videoUrlctrl,
                decoration: const InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                        borderRadius: BorderRadius.all(Radius.circular(10))),
                    labelText: 'Video URL'),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text("Special"),
                  Checkbox(
                    value:
                        isNew ? videoIsChecked : ctrl.gallery[index].isSpecial,
                    onChanged: (bool? value) {
                      setState2(() {
                        videoIsChecked = value!;
                        ctrl.gallery[index].isSpecial = value;
                      });
                    },
                  )
                ],
              ),
              const SizedBox(height: 12),
              onSubmitLoad
                  ? const Center(
                      child: CircularProgressIndicator(
                          color: Colors.red, strokeWidth: 3.5))
                  : ElevatedButton(
                      style: ButtonStyle(
                          backgroundColor:
                              const WidgetStatePropertyAll(Colors.red),
                          padding: const WidgetStatePropertyAll(
                              EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 20)),
                          side: const WidgetStatePropertyAll(
                              BorderSide(color: Colors.transparent)),
                          shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ))),
                      onPressed: () async {
                        if (videotitlectrl.text.isEmpty ||
                            videodesctrl.text.isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content:
                                      Text("Please enter all the fields")));
                          return;
                        }
                        onSubmitLoad = true;
                        try {
                          setState2(() {
                            onSubmitLoad = true;
                          });
                          isNew
                              ? FBFireStore.gallery.add({
                                  'title': videotitlectrl.text.isNotEmpty
                                      ? videotitlectrl.text
                                      : "-",
                                  'thumbnail':
                                      videothumbnailctrl.text.isNotEmpty
                                          ? videothumbnailctrl.text
                                          : "-",
                                  'imageUrl': videoUrlctrl.text.isNotEmpty
                                      ? videoUrlctrl.text
                                      : "-",
                                  'hasImage': false,
                                  'desc': videodesctrl.text.isNotEmpty
                                      ? videodesctrl.text
                                      : "-",
                                  'isSpecial': videoIsChecked,
                                  'isVisible': true,
                                  'isFav': false
                                })
                              : FBFireStore.gallery
                                  .doc(ctrl.gallery[index].docId)
                                  .update({
                                  'title': videotitlectrl.text,
                                  'thumbnail': videothumbnailctrl.text,
                                  'imageUrl': videoUrlctrl.text,
                                  'hasImage': false,
                                  'desc': videodesctrl.text,
                                  'isSpecial': videoIsChecked,
                                  'isVisible': true,
                                  'isFav': false
                                });

                          Navigator.pop(context);
                          showAppSnackBar("Video Content Added Successfully");
                          print("video data updated");
                        } catch (e) {
                          print("error uploading video");
                        }
                      },
                      child: const Text("Submit Video",
                          style: TextStyle(color: Colors.white, fontSize: 16))),
            ],
          ),
        ),
      );
    }),
  );
}

Future<dynamic> addImageDialog(BuildContext context, bool onSubmitLoad,
    bool isNew, Homectrl ctrl, int index) {
  TextEditingController titlectrl = TextEditingController();
  // TextEditingController imageUrlctrl = TextEditingController();
  TextEditingController descctrl = TextEditingController();
  TextEditingController thumbnailctrl = TextEditingController();

  SelectedImage? imageFileList;
  String? image;
  List<String> imageurl = [];
  Future<String?> uploadImage(SelectedImage imageFile) async {
    try {
      final path =
          "Images/${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}";
      final imageRef = FBStorage.fbstore.ref().child(path);

      final task = await imageRef.putData(imageFile.uInt8List);
      var downloadurl = await task.ref.getDownloadURL();
      imageurl.add(downloadurl);

      return await task.ref.getDownloadURL();
    } on Exception catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  bool imageIsChecked = false;
  imageFileList = null;
  bool onImageSubmitLoad = false;

  if (!isNew) {
    titlectrl.text = ctrl.gallery[index].title;
    descctrl.text = ctrl.gallery[index].desc;
    thumbnailctrl.text = ctrl.gallery[index].thumbnail!;

    image = ctrl.gallery[index].imageUrl;
  }

  return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text("Add Image"),
                IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.close),
                )
              ],
            ),
            content: SizedBox(
                width: 600,
                // height: MediaQuery.sizeOf(context).height,
                height: 350,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    TextFormField(
                      controller: titlectrl,
                      decoration: const InputDecoration(
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Title'),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: descctrl,
                      decoration: const InputDecoration(
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Description'),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: thumbnailctrl,
                      decoration: const InputDecoration(
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Thumbnail'),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text("Special"),
                        Checkbox(
                          value: isNew
                              ? imageIsChecked
                              : ctrl.gallery[index].isSpecial,
                          onChanged: (bool? value) {
                            setState2(() {
                              imageIsChecked = value!;
                            });
                            ctrl.gallery[index].isSpecial = value;
                          },
                        )
                      ],
                    ),
                    const SizedBox(height: 12),
                    imageFileList != null
                        ? Stack(
                            alignment: Alignment.topRight,
                            children: [
                              Image.memory(
                                imageFileList!.uInt8List,
                                height: 50,
                                width: 50,
                                fit: BoxFit.cover,
                              ),
                              IconButton(
                                icon: const Icon(
                                  Icons.cancel,
                                  color: Colors.red,
                                ),
                                onPressed: () async {
                                  setState2(() {
                                    imageFileList = null;
                                  });

                                  try {
                                    if (imageFileList != null) {
                                      final path =
                                          "Images/${DateTime.now().millisecondsSinceEpoch}.${imageFileList!.extention}";
                                      final imageRef =
                                          FBStorage.fbstore.ref().child(path);
                                      await imageRef.delete();
                                      showAppSnackBar(
                                          "Image removed successfully.");
                                    }
                                  } catch (e) {
                                    showAppSnackBar("Error removing image.");
                                  }
                                },
                              ),
                            ],
                          )
                        : image != null
                            ? Stack(
                                alignment: Alignment.topRight,
                                children: [
                                  Image.network(
                                    image!,
                                    height: 50,
                                    width: 50,
                                    fit: BoxFit.cover,
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.cancel,
                                      color: Colors.red,
                                    ),
                                    onPressed: () async {
                                      setState2(() {
                                        image = null;
                                      });
                                      // try {
                                      //   if (imageFileList != null) {
                                      //     final path =
                                      //         "Images/${DateTime.now().millisecondsSinceEpoch}.${imageFileList!.extention}";
                                      //     final imageRef =
                                      //         FBStorage.fbstore.ref().child(path);
                                      //     await imageRef.delete();
                                      //     showAppSnackBar(
                                      //         "Image removed successfully.");
                                      //   }
                                      // } catch (e) {
                                      //   showAppSnackBar("Error removing image.");
                                      // }
                                    },
                                  ),
                                ],
                              )
                            : ElevatedButton(
                                onPressed: () async {
                                  imageFileList = await ImagePickerService()
                                      .pickImageNew(context,
                                          useCompressor: true);
                                  setState2(() {});
                                },
                                child: const Icon(CupertinoIcons
                                    .photo_fill_on_rectangle_fill),
                              ),
                    const SizedBox(height: 12),
                    onImageSubmitLoad
                        ? const Center(
                            child: CircularProgressIndicator(
                                color: Colors.red, strokeWidth: 3.5))
                        : ElevatedButton(
                            style: ButtonStyle(
                                backgroundColor:
                                    const WidgetStatePropertyAll(Colors.red),
                                padding: const WidgetStatePropertyAll(
                                    EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 20)),
                                side: const WidgetStatePropertyAll(
                                    BorderSide(color: Colors.transparent)),
                                shape: WidgetStatePropertyAll(
                                    RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5),
                                ))),
                            onPressed: () async {
                              if (titlectrl.text.isEmpty ||
                                  descctrl.text.isEmpty ||
                                  thumbnailctrl.text.isEmpty ||
                                  imageFileList == null && image == null) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text(
                                            "Please enter all the fields")));
                                return;
                              }

                              onImageSubmitLoad = true;
                              try {
                                setState2(() {
                                  onImageSubmitLoad = true;
                                });
                                String? finalImageUrl;
                                if (imageFileList != null) {
                                  finalImageUrl =
                                      await uploadImage(imageFileList!);
                                }
                                isNew
                                    ? await FBFireStore.gallery.add({
                                        'title': titlectrl.text.isNotEmpty
                                            ? titlectrl.text
                                            : "-",
                                        'imageUrl': finalImageUrl ?? "-",
                                        'desc': descctrl.text.isNotEmpty
                                            ? descctrl.text
                                            : "-",
                                        'hasImage': true,
                                        'thumbnail':
                                            thumbnailctrl.text.isNotEmpty
                                                ? thumbnailctrl.text
                                                : "-",
                                        'isSpecial': imageIsChecked,
                                        'isVisible': true,
                                        'isFav': false
                                      })
                                    : await FBFireStore.gallery
                                        .doc(ctrl.gallery[index].docId)
                                        .update({
                                        'title': titlectrl.text,
                                        'imageUrl': finalImageUrl ?? image,
                                        'desc': descctrl.text,
                                        'hasImage': true,
                                        'thumbnail': thumbnailctrl.text,
                                        'isSpecial': imageIsChecked,
                                        'isVisible': true,
                                        'isFav': false
                                      });
                                Navigator.pop(context);
                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                    content: Text(isNew
                                        ? "Image Content Successfully Added"
                                        : "Image Content Successfully Updated")));

                                print("image content added");
                              } catch (e) {
                                debugPrint(e.toString());
                                print("Error posting image");
                              }
                            },
                            child: const Text("Submit Image",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 16))),
                    const SizedBox(width: 15),
                  ],
                )),
          );
        });
      });
}
