import 'dart:convert';

class BlogModel {
  String? id;
  bool? isVisible;
  String? metaTitle;
  String? lowerTitle;
  String? description;
  String? markdown;
  String? htmlString;
  List<String>? tags;
  String? permalink;
  List<String>? imagesLinks;
  String? thumbnail;
  List<BlogModel>? otherBlogs;
  String? createdAt;
  String? updatedAt;
  bool? isFav;

  BlogModel(
      {this.id,
      this.isVisible,
      this.metaTitle,
      this.lowerTitle,
      this.description,
      this.markdown,
      this.htmlString,
      this.tags,
      this.permalink,
      this.imagesLinks,
      this.thumbnail,
      this.otherBlogs,
      this.createdAt,
      this.updatedAt,
      this.isFav});

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (id != null) {
      result.addAll({'id': id});
    }
    if (isVisible != null) {
      result.addAll({'isVisible': isVisible});
    }
    if (isFav != null) {
      result.addAll({'isFav': isFav});
    }
    if (metaTitle != null) {
      result.addAll({'metaTitle': metaTitle});
    }
    if (lowerTitle != null) {
      result.addAll({'lowerTitle': lowerTitle});
    }
    if (description != null) {
      result.addAll({'description': description});
    }
    if (markdown != null) {
      result.addAll({'markdown': markdown});
    }
    if (htmlString != null) {
      result.addAll({'htmlString': htmlString});
    }
    if (tags != null) {
      result.addAll({'tags': tags});
    }
    if (permalink != null) {
      result.addAll({'permalink': permalink});
    }
    if (imagesLinks != null) {
      result.addAll({'imagesLinks': imagesLinks});
    }
    if (thumbnail != null) {
      result.addAll({'thumbnail': thumbnail});
    }
    if (otherBlogs != null) {
      result.addAll({'otherBlogs': otherBlogs!.map((x) => x.toMap()).toList()});
    }
    if (createdAt != null) {
      result.addAll({'createdAt': createdAt});
    }
    if (updatedAt != null) {
      result.addAll({'updatedAt': updatedAt});
    }

    return result;
  }

  factory BlogModel.fromMap(Map<String, dynamic> map) {
    return BlogModel(
      id: map['id'],
      isVisible: map['isVisible'] ?? true,
      isFav: map['isFav'] ?? false,
      metaTitle: map['metaTitle'],
      lowerTitle: map['lowerTitle'],
      description: map['description'],
      markdown: map['markdown'],
      htmlString: map['htmlString'],
      tags: map['tags'] != null ? List<String>.from(map['tags']) : [],
      permalink: map['permalink'],
      imagesLinks: map['imagesLinks'] != null
          ? List<String>.from(map['imagesLinks'])
          : null,
      thumbnail: map['thumbnail'],
      otherBlogs: map['otherBlogs'] != null
          ? List<BlogModel>.from(
              map['otherBlogs']?.map((x) => BlogModel.fromMap(x)))
          : null,
      createdAt: map['createdAt'],
      updatedAt: map['updatedAt'],
    );
  }

  String toJson() => json.encode(toMap());

  factory BlogModel.fromJson(String source) =>
      BlogModel.fromMap(json.decode(source));
}

class BlogImage {
  String? imageUrl;
  String? altText;
  BlogImage({
    this.imageUrl,
    this.altText,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (imageUrl != null) {
      result.addAll({'imageUrl': imageUrl});
    }
    if (altText != null) {
      result.addAll({'altText': altText});
    }

    return result;
  }

  factory BlogImage.fromMap(Map<String, dynamic> map) {
    return BlogImage(
      imageUrl: map['imageUrl'],
      altText: map['altText'],
    );
  }

  String toJson() => json.encode(toMap());

  factory BlogImage.fromJson(String source) =>
      BlogImage.fromMap(json.decode(source));
}
