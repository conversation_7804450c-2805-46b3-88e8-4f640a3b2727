import 'package:cloud_firestore/cloud_firestore.dart';

class GalleryModel {
  final String docId;
  final String title;
  final String desc;
  final String? thumbnail;
  final String imageUrl;
  final bool hasImage;
  bool? isSpecial;
  bool? isVisible;
  bool? isFav;

  GalleryModel(
      {required this.docId,
      required this.desc,
      required this.title,
      this.thumbnail,
      required this.imageUrl,
      required this.hasImage,
      this.isSpecial,
      this.isVisible,
      this.isFav});

  factory GalleryModel.fromSnap(DocumentSnapshot snap) {
    return GalleryModel(
      title: snap['title'] as String,
      thumbnail: snap['thumbnail'] as String,
      imageUrl: snap['imageUrl'] as String,
      hasImage: snap['hasImage'] as bool,
      desc: snap['desc'] as String,
      isSpecial: snap['isSpecial'] as bool,
      isVisible: snap['isVisible'] as bool,
      isFav: snap['isFav'] as bool,
      docId: snap.id,
    );
  }

  Map<String, dynamic> toSnap() {
    return {
      'title': title ?? "",
      'thumbnail': thumbnail ?? "",
      'imageUrl': imageUrl ?? "",
      'hasImage': hasImage,
      'desc': desc ?? "",
      'isSpecial': isSpecial,
      'isVisible': isVisible,
      'isFav': isFav,
      'docId': docId,
    };
  }

  factory GalleryModel.fromJson(Map<String, dynamic> json) {
    return GalleryModel(
      title: json['title'] as String,
      thumbnail: json['thumbnail'] as String,
      imageUrl: json['imageUrl'] as String,
      hasImage: json['hasImage'] as bool,
      desc: json['desc'] as String,
      isSpecial: json['isSpecial'] as bool,
      isVisible: json['isVisible'] as bool,
      isFav: json['isFav'] as bool,
      docId: json['docId'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'thumbnail': thumbnail ?? "",
      'imageUrl': imageUrl,
      'hasImage': hasImage,
      'desc': desc,
      'isSpecial': isSpecial,
      'docId': docId,
      'isVisible': isVisible,
      'isFav': isFav,
    };
  }
}
