import 'package:flutter/material.dart';
import 'package:luxor_admin/configurations/app_configurations.dart';
import 'package:intl/intl.dart';

enum SnackBarType { success, info, error }

class Utils {
  static showSnackbar({
    required BuildContext context,
    SnackBarType type = SnackBarType.info,
    Widget? contentWidget,
    String? message,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: contentWidget ?? Text(message ?? ""),
        backgroundColor: _getSnackBarBGColor(type),
      ),
    );
  }

  static _getSnackBarBGColor(SnackBarType type) {
    switch (type) {
      case SnackBarType.success:
        return colorsConstants.successGreen;
      case SnackBarType.info:
        return colorsConstants.infoYellow;
      case SnackBarType.error:
        return colorsConstants.errorRed;
    }
  }

  /// Formats a date string to a readable format
  /// Returns formatted date like "Dec 15, 2023" or "Unknown Date" if parsing fails
  static String formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return "Unknown Date";
    }

    try {
      DateTime date = DateTime.parse(dateString);
      return DateFormat('MMM dd, yyyy').format(date);
    } catch (e) {
      return "Unknown Date";
    }
  }

  /// Formats a date string to a relative format (e.g., "2 days ago")
  static String formatRelativeDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return "Unknown Date";
    }

    try {
      DateTime date = DateTime.parse(dateString);
      DateTime now = DateTime.now();
      Duration difference = now.difference(date);

      if (difference.inDays > 365) {
        int years = (difference.inDays / 365).floor();
        return "$years year${years > 1 ? 's' : ''} ago";
      } else if (difference.inDays > 30) {
        int months = (difference.inDays / 30).floor();
        return "$months month${months > 1 ? 's' : ''} ago";
      } else if (difference.inDays > 0) {
        return "${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago";
      } else if (difference.inHours > 0) {
        return "${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago";
      } else if (difference.inMinutes > 0) {
        return "${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago";
      } else {
        return "Just now";
      }
    } catch (e) {
      return "Unknown Date";
    }
  }
}
