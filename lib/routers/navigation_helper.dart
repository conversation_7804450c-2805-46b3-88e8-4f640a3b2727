import 'package:luxor_admin/models/blog_model.dart';
import 'package:flutter/material.dart';
import 'package:luxor_admin/enums/enums.dart';
import 'package:luxor_admin/routers/extensions.dart';

moveToLoginScreen(
    {required BuildContext context, NavigationOperations? operation}) {
  context.operation(
    operation: operation ?? NavigationOperations.pushNamed,
    routeScreen: RouteScreens.login,
  );
}

moveToBaseModuleScreen(
    {required BuildContext context, NavigationOperations? operation}) {
  context.operation(
    operation: operation ?? NavigationOperations.pushNamed,
    routeScreen: RouteScreens.baseModule,
  );
}

moveToDashboardScreen(
    {required BuildContext context, NavigationOperations? operation}) {
  context.operation(
    operation: operation ?? NavigationOperations.pushNamed,
    routeScreen: RouteScreens.dashboard,
  );
}

moveToAddEditBlogScreen({
  required BuildContext context,
  BlogModel? blogModel,
  List<BlogModel>? allBlogs,
  NavigationOperations? operation,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: operation ?? NavigationOperations.pushNamed,
      routeScreen: RouteScreens.addEditBlog,
      settings: SettingArguments(
        argumentsData: {"blogModel": blogModel, "allBlogs": allBlogs},
      ),
      revertCallback: (data) {
        // handle setup if required
        print("back");

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          print("null");
        }
      });
}
