import 'package:collection/collection.dart';
import 'package:luxor_admin/modules/addEditBlog/views/add_edit_blog.dart';
import 'package:luxor_admin/modules/dashboard/views/dashboard.dart';
import 'package:luxor_admin/routers/navigation_helper.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:flutter/material.dart';
import 'package:luxor_admin/enums/enums.dart';
import 'package:luxor_admin/modules/splash/views/splash_screen.dart';
import 'package:luxor_admin/routers/extensions.dart';

class NavigationRoutesGenerator {
  static Route<dynamic> onGenerateRoute(RouteSettings routeSettings) {
    final RouteScreens routeScreen = RouteScreens.values.firstWhereOrNull(
            (element) => element.name == routeSettings.name) ??
        RouteScreens.root;

    SettingArguments? args;
    if (routeSettings.arguments != null) {
      args = routeSettings.arguments as SettingArguments?;
    }

    switch (routeScreen) {
      case RouteScreens.splash:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const SplashScreen();
          },
        );
      case RouteScreens.root:
      case RouteScreens.login:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return SignInScreen(
              showAuthActionSwitch: false,
              showPasswordVisibilityToggle: true,
              providers: [EmailAuthProvider()],
              actions: [
                AuthStateChangeAction<SignedIn>((context, state) {
                  moveToDashboardScreen(
                      context: context,
                      operation: NavigationOperations.pushReplacementNamed);
                }),
                AuthStateChangeAction<SigningUp>((context, state) {
                  moveToDashboardScreen(
                      context: context,
                      operation: NavigationOperations.pushReplacementNamed);
                }),
              ],
            );
          },
        );

      case RouteScreens.baseModule:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const Placeholder();
          },
        );

      case RouteScreens.dashboard:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const Dashboard();
          },
        );
      case RouteScreens.addEditBlog:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            Map<String, dynamic>? argsData;
            if (args?.argumentsData is Map<String, dynamic>) {
              argsData = args?.argumentsData as Map<String, dynamic>;
            }

            return AddEditBlog(
              blogModel: argsData != null ? argsData["blogModel"] : null,
              allBlogs: argsData != null ? argsData["allBlogs"] : null,
            );
          },
        );
    }
  }
}
