abstract class IAppAssetsConstants {
  factory IAppAssetsConstants.getInstance() => AppAssetsConstants();

  String get basePath;
  String get icons;
  String get iconsSvg;
  String get images;
  String get imagesSvg;

  String get noImagePlaceholder;

  String get headingTips;
  String get paragraphTips;
  String get boldTips;
  String get italicTips;
  String get boldItalicTips;
  String get blockquotesTips;
  String get blockquotesMultiParagraphTips;
  String get blockquotesWithOtherElementTips;
  String get nestedBlockquotesTips;
  String get orderedListTips;
  String get unorderedListTips;
  String get startingUlItemWithNumberTips;
  String get imagesTip;
  String get imageSizeTip;
  String get linkTip;
  String get linkTargetTip;
  String get admonitionsTip;
  String get logo;
}

class AppAssetsConstants implements IAppAssetsConstants {
  @override
  String get basePath => "assets";

  @override
  String get icons => "$basePath/icons";

  @override
  String get iconsSvg => "$icons/svg";

  @override
  String get images => "$basePath/images";

  @override
  String get imagesSvg => "$images/svg";

  @override
  String get noImagePlaceholder => "$imagesSvg/no_image_placeholder.svg";

  @override
  String get headingTips => "$images/headings.png";

  @override
  String get paragraphTips => "$images/paragraphs.png";

  @override
  String get blockquotesTips => "$images/blockquotes.png";

  @override
  String get blockquotesMultiParagraphTips =>
      "$images/blockquotes_multi_paragraph.png";

  @override
  String get blockquotesWithOtherElementTips =>
      "$images/blockquotes_with_other_element.png";

  @override
  String get boldTips => "$images/bold.png";

  @override
  String get boldItalicTips => "$images/bold_italic.png";

  @override
  String get italicTips => "$images/italic.png";

  @override
  String get nestedBlockquotesTips => "$images/nested_blockquotes.png";

  @override
  String get orderedListTips => "$images/ordered_list.png";

  @override
  String get startingUlItemWithNumberTips =>
      "$images/starting_ul_item_with_number.png";

  @override
  String get unorderedListTips => "$images/unordered_list.png";

  @override
  String get imagesTip => "$images/images_tip.png";

  @override
  String get imageSizeTip => "$images/image_size.png";

  @override
  String get admonitionsTip => "$images/admonitions.png";

  @override
  String get linkTargetTip => "$images/link_target.png";

  @override
  String get linkTip => "$images/link.png";

  @override
  String get logo => "$images/R.png";
}
