class GalleryModel {
  final String title;
  final String desc;
  final String? thumbnail;
  final String imageUrl;
  final bool hasImage;
  bool? isSpecial;

  GalleryModel({
    required this.desc,
    required this.title,
    this.thumbnail,
    required this.imageUrl,
    required this.hasImage,
    this.isSpecial,
  });

  factory GalleryModel.fromSnap(Map<String, dynamic> snap) {
    return GalleryModel(
      title: snap['title'] as String,
      thumbnail: snap['thumbnail'] as String,
      imageUrl: snap['imageUrl'] as String,
      hasImage: snap['hasImage'] as bool,
      desc: snap['desc'] as String,
      isSpecial: snap['isSpecial'] as bool,
    );
  }

  Map<String, dynamic> toSnap() {
    return {
      'title': title,
      'thumbnail': thumbnail ?? "",
      'imageUrl': imageUrl,
      'hasImage': hasImage,
      'desc': desc,
      'isSpecial': isSpecial
    };
  }

  factory GalleryModel.fromJson(Map<String, dynamic> json) {
    return GalleryModel(
      title: json['title'] as String,
      thumbnail: json['thumbnail'] as String,
      imageUrl: json['imageUrl'] as String,
      hasImage: json['hasImage'] as bool,
      desc: json['desc'] as String,
      isSpecial: json['isSpecial'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'thumbnail': thumbnail ?? "",
      'imageUrl': imageUrl,
      'hasImage': hasImage,
      'desc': desc,
      'isSpecial': isSpecial,
    };
  }
}
