import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../../models/blogsmodel.dart';
import '../../../shared/image_picker.dart';

class AddEditBlogRepository {
  Future<void> addBlog({required BlogsModel blogModel}) async {
    try {
      if (blogModel.id != null && blogModel.id!.isNotEmpty) {
        blogModel.updatedAt = DateTime.now().toIso8601String();
        blogModel.lowerTitle = blogModel.metaTitle?.toLowerCase();
        await FirebaseFirestore.instance
            .collection("Blogs")
            .doc(blogModel.id)
            .update(blogModel.toMap());
      } else {
        blogModel.createdAt = DateTime.now().toIso8601String();
        blogModel.updatedAt = DateTime.now().toIso8601String();
        blogModel.lowerTitle = blogModel.metaTitle?.toLowerCase();
        await FirebaseFirestore.instance
            .collection("Blogs")
            .add(blogModel.toMap());
      }
    } catch (e) {
      print(e);
    }
  }

  Future<List<String>> uploadImages(
      {required List<SelectedImage> imagesToUpload}) async {
    List<String> uploadedFilesLinks = [];
    try {
      for (var image in imagesToUpload) {
        await FirebaseStorage.instance
            .ref()
            .child(image.name)
            .putData(
                image.uInt8List, SettableMetadata(contentType: 'image/jpeg'))
            .then((TaskSnapshot taskSnapshot) async {
          uploadedFilesLinks.add(await taskSnapshot.ref.getDownloadURL());
        });
      }
    } catch (e) {
      print(e);
    }
    return uploadedFilesLinks;
  }

  Future<String?> getImageUrl({required String path}) async {
    try {
      String downloadImageUrl =
          await FirebaseStorage.instance.ref(path).getDownloadURL();
      return downloadImageUrl;
    } catch (e) {
      print(e);
    }
    return null;
  }
}
