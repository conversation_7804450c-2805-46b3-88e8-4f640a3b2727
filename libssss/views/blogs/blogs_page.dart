// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:go_router/go_router.dart';
// import 'package:luxor_admin/shared/router.dart';
// import 'package:luxor_admin/views/dashboard/repositories/dashboard_repository.dart';
// import 'package:responsive_grid_list/responsive_grid_list.dart';
// import '../../models/blogsmodel.dart';
// import '../../routers/navigation_helper.dart';
// import '../../utils/utils.dart';
// import '../dashboard/bloc/dashboard_bloc.dart';
// import '../dashboard/views/dashboard.dart';
// import '../widgets/height_box.dart';
// import '../widgets/primary_button.dart';

// class BlogsPage extends StatefulWidget {
//   const BlogsPage({super.key});

//   @override
//   State<BlogsPage> createState() => _BlogsPageState();
// }

// class _BlogsPageState extends State<BlogsPage> {
//   @override
//   Widget build(BuildContext context) {
//     return BlocProvider(
//       create: (context) =>
//           DashboardBloc(dashboardRepository: DashboardRepository())
//             ..add(LoadDashboardData()),
//       child: Builder(
//         builder: (context) {
//           DashboardBloc dashboardBloc = context.read<DashboardBloc>();

//           return Scaffold(
//             appBar: AppBar(
//               elevation: 5,
//               shadowColor: Colors.black12,
//               toolbarHeight: 50,
//               leadingWidth: 30,
//               backgroundColor: Color(0xff68b38c),
//               title: const Padding(
//                   padding: EdgeInsets.only(left: 60), child: SizedBox()
//                   // Image.asset(
//                   //   height: 70,
//                   //   // assetsConstants.logo,
//                   //   // fit: BoxFit.fill,
//                   //   color: Colors.white,
//                   // ),
//                   ),
//               // leading:
//               actions: [
//                 ElevatedButton(
//                     style: const ButtonStyle(
//                         shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
//                             borderRadius:
//                                 BorderRadius.all(Radius.circular(5)))),
//                         padding: WidgetStatePropertyAll(
//                             EdgeInsets.symmetric(vertical: 10, horizontal: 10)),
//                         backgroundColor: WidgetStatePropertyAll(Colors.white)),
//                     onPressed: () {
//                       context.go(Routes.addEditBlog);
//                     },
//                     child: const Text(
//                       "Add Blog",
//                       style: TextStyle(
//                           color: Colors.black, fontWeight: FontWeight.w500),
//                     )),
//                 const SizedBox(width: 20)
//               ],
//             ),
//             body: BlocConsumer<DashboardBloc, DashboardState>(
//               listener: (context, state) {
//                 if (state is DashboardErrorState) {
//                   Utils.showSnackbar(
//                       context: context,
//                       message: state.errorData?.data["message"]);
//                 } else if (state is BlogDeletedState) {
//                   Navigator.pop(context);
//                   context.read<DashboardBloc>().add(LoadDashboardData());
//                 }
//               },
//               builder: (context, state) {
//                 if (state is LoadingDashboardDataState) {
//                   return const Center(
//                     child: CircularProgressIndicator(color: Colors.blue),
//                   );
//                 }

//                 if (state is DashboardDataLoadedState &&
//                     state.allBlogs.isEmpty) {
//                   return Center(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Text(
//                           "No data found",
//                           style: TextStyle(fontSize: 5.sp),
//                         ),
//                         PrimaryButton(
//                           onTap: () {
//                             moveToAddEditBlogScreen(
//                               context: context,
//                               allBlogs: dashboardBloc.state.allBlogs,
//                               revertCallback: (_) {
//                                 dashboardBloc.add(LoadDashboardData());
//                               },
//                             );
//                           },
//                           title: "Add Blog",
//                           titleStyle:
//                               TextStyle(fontSize: 4.sp, color: Colors.white),
//                           width: 24.w,
//                           margin: EdgeInsets.symmetric(vertical: 5.sp),
//                           padding: EdgeInsets.symmetric(vertical: 2.sp),
//                         ),
//                       ],
//                     ),
//                   );
//                 }

//                 // return SizedBox();

//                 return ResponsiveGridList(
//                   horizontalGridSpacing: 8.sp,
//                   verticalGridSpacing: 8.sp,
//                   horizontalGridMargin: 10.sp,
//                   verticalGridMargin: 10.sp,
//                   minItemWidth: 20.sp,
//                   minItemsPerRow: 2,
//                   maxItemsPerRow: 5,
//                   listViewBuilderOptions: ListViewBuilderOptions(
//                       shrinkWrap:
//                           true), // Options that are getting passed to the ListView.builder() function
//                   children: state.allBlogs.map((BlogsModel blogModel) {
//                     // print("${blogModel.thumbnail}...........blogmodel");
//                     return Card(
//                       elevation: 2,
//                       clipBehavior: Clip.antiAlias,
//                       color: Colors.white,
//                       surfaceTintColor: Colors.white,
//                       margin: EdgeInsets.zero,
//                       child: Stack(
//                         children: [
//                           InkWell(
//                             onTap: () {
//                               moveToAddEditBlogScreen(
//                                 context: context,
//                                 blogModel: blogModel,
//                                 allBlogs: dashboardBloc.state.allBlogs,
//                                 revertCallback: (_) {
//                                   dashboardBloc.add(LoadDashboardData());
//                                 },
//                               );
//                             },
//                             onLongPress: () {
//                               showDialog(
//                                 context: context,
//                                 builder: (context) {
//                                   return DeletePopup(
//                                     dashboardBloc: dashboardBloc,
//                                     blogModel: blogModel,
//                                   );
//                                 },
//                               );
//                             },
//                             hoverColor: Colors.transparent,
//                             splashColor: Colors.transparent,
//                             child: Column(
//                               mainAxisAlignment: MainAxisAlignment.start,
//                               mainAxisSize: MainAxisSize.min,
//                               children: [
//                                 Stack(
//                                   children: [
//                                     Image.network(
//                                       blogModel.thumbnail ?? "",
//                                       height: 50.sp,
//                                       width: 70.sp,
//                                       fit: BoxFit.cover,
//                                       // errorBuilder:
//                                       //     (context, error, stackTrace) {
//                                       //   return Center(
//                                       //     child: Icon(Icons.error),
//                                       //   );
//                                       // },
//                                     ),
//                                     Container(
//                                       height: 50.sp,
//                                       width: 70.sp,
//                                       decoration: BoxDecoration(
//                                         gradient: LinearGradient(
//                                           begin: Alignment.topCenter,
//                                           end: Alignment.bottomCenter,
//                                           colors: [
//                                             Colors.black.withOpacity(0.3),
//                                             Colors.black.withOpacity(0.0)
//                                           ],
//                                         ),
//                                       ),
//                                     )
//                                   ],
//                                 ),
//                                 HeightBox(2.sp),
//                                 Padding(
//                                   padding:
//                                       EdgeInsets.symmetric(horizontal: 2.w),
//                                   child: Text(
//                                     softWrap: true,
//                                     blogModel.metaTitle ?? "",
//                                     maxLines: 3,
//                                     overflow: TextOverflow.ellipsis,
//                                     style: TextStyle(fontSize: 3.sp),
//                                   ),
//                                 ),
//                                 HeightBox(2.sp),
//                               ],
//                             ),
//                           ),
//                           Positioned(
//                             right: 1.5.sp,
//                             top: 1.sp,
//                             child: InkWell(
//                               onTap: () {
//                                 dashboardBloc.add(
//                                   ShowHideBlogEvent(
//                                     blogModel: blogModel,
//                                   ),
//                                 );
//                               },
//                               child: Icon(
//                                 blogModel.isVisible == true
//                                     ? Icons.visibility_rounded
//                                     : Icons.visibility_off_rounded,
//                                 color: Colors.white,
//                                 size: 5.sp,
//                               ),
//                             ),
//                           )
//                         ],
//                       ),
//                     );
//                   }).toList(), // The list of widgets in the list
//                 );
//               },
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
