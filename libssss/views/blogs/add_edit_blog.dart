import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:markdown/markdown.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:textfield_tags/textfield_tags.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import '../../configurations/app_configurations.dart';
import '../../models/blogsmodel.dart';
import '../../shared/image_picker.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/height_box.dart';
import '../widgets/primary_button.dart';
import '../widgets/width_box.dart';
import 'bloc/add_edit_blog_bloc.dart';
import 'repositories/add_edit_blog_repository.dart';
import 'package:flutter/src/widgets/text.dart' as TextWidget;

class AddEditBlog extends StatefulWidget {
  const AddEditBlog({super.key, this.blogModel, this.allBlogs});

  final BlogsModel? blogModel;
  final List<BlogsModel>? allBlogs;

  @override
  State<AddEditBlog> createState() => _AddEditBlogState();
}

HtmlEditorController hcontroller = HtmlEditorController();

class _AddEditBlogState extends State<AddEditBlog> {
  late WebViewXController webviewController;
  final scrollController = ScrollController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocProvider(
        create:
            (context) => AddEditBlogBloc(
              addEditBlogRepository: AddEditBlogRepository(),
              imagePickerService: ImagePickerService(),
            ),
        child: Builder(
          builder: (context) {
            AddEditBlogBloc addEditBlogBloc = context.read<AddEditBlogBloc>();

            if (widget.blogModel != null) {
              BlogsModel blogModel = widget.blogModel!;

              addEditBlogBloc.metaTitleTextEditingController.text =
                  blogModel.metaTitle ?? "";
              addEditBlogBloc.markdownNotifier.value =
                  addEditBlogBloc.markdownTextEditingController.text =
                      blogModel.markdown ?? "";
              addEditBlogBloc.thumbnailTextEditingController.text =
                  blogModel.thumbnail ?? "";
              addEditBlogBloc.permaLinkTextEditingController.text =
                  blogModel.permalink ?? "";
              addEditBlogBloc.uploadedImages = blogModel.imagesLinks ?? [];
              if (blogModel.tags != null && blogModel.tags!.isNotEmpty) {
                addEditBlogBloc.initialTags.addAll(blogModel.tags!);
              }

              addEditBlogBloc.otherBlogs = blogModel.otherBlogs ?? [];

              addEditBlogBloc.allBlogs = widget.allBlogs ?? [];

              BlogsModel blogModel2 = widget.blogModel!;

              addEditBlogBloc.metaTitleTextEditingController.text =
                  blogModel2.metaTitle ?? "";
              addEditBlogBloc.markdownNotifier.value =
                  addEditBlogBloc.markdownTextEditingController.text =
                      blogModel2.markdown ?? "";
              addEditBlogBloc.thumbnailTextEditingController.text =
                  blogModel2.thumbnail ?? "";
              addEditBlogBloc.permaLinkTextEditingController.text =
                  blogModel2.permalink ?? "";
              addEditBlogBloc.uploadedImages = blogModel2.imagesLinks ?? [];
              // selectedDate = DateTime.parse(blogModel.createdAt ?? "");
              if (blogModel2.tags != null && blogModel2.tags!.isNotEmpty) {
                addEditBlogBloc.initialTags.addAll(blogModel.tags!);
              }
              // print("Start");
              // print(addEditBlogBloc.otherBlogs.length);
              addEditBlogBloc.otherBlogs = blogModel2.otherBlogs ?? [];
              // print("ENd");
              // print(addEditBlogBloc.otherBlogs.length);

              addEditBlogBloc.allBlogs = widget.allBlogs ?? [];

              if (widget.allBlogs != null) {
                addEditBlogBloc.allBlogs = widget.allBlogs ?? [];

                // Initialize the multiDropdownController with selected items
                List<DropdownItem<String>> selectedItems = [];

                for (var otherBlog in addEditBlogBloc.otherBlogs) {
                  selectedItems.add(
                    DropdownItem<String>(
                      value: otherBlog.id.toString(),
                      label: otherBlog.metaTitle ?? '',
                    ),
                  );
                }

                if (selectedItems.isNotEmpty) {
                  addEditBlogBloc.multiDropdownController.items.addAll(
                    selectedItems,
                  );
                }
              }
            }

            /*     if (widget.allBlogs != null) {
                List<ValueItem> allValueItems = [];

                for (var element in widget.allBlogs!) {
                  allValueItems.add(
                    ValueItem(
                      label: element.metaTitle ?? "",
                      value: element.id,
                    ),
                  );
                }
                addEditBlogBloc.otherBlogsSelectController.setOptions(
                  allValueItems,
                );

                for (ValueItem option
                    in addEditBlogBloc.otherBlogsSelectController.options) {
                  if (addEditBlogBloc.otherBlogs.firstWhereOrNull(
                        (element) => element.id == option.value,
                      ) !=
                      null) {
                    addEditBlogBloc.otherBlogsSelectController
                        .addSelectedOption(option);
                  }
                }
              } */
            // }

            return BlocConsumer<AddEditBlogBloc, AddEditBlogState>(
              listener: (context, state) {
                if (state is BlogAddedState) {
                  Navigator.pop(context);
                }
              },
              builder: (context, state) {
                return Container(
                  padding: const EdgeInsets.only(left: 8),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            const HeightBox(10),
                            PrimaryButton(
                              title: "Upload Images",
                              onTap:
                                  state is UploadingImagesState
                                      ? () {}
                                      : () async {
                                        List<SelectedImage> pickedImages =
                                            await addEditBlogBloc
                                                .imagePickerService
                                                .pickImageAndCompress(
                                                  useCompressor: true,
                                                );

                                        addEditBlogBloc.add(
                                          UploadImagesEvent(
                                            imagesToUpload: pickedImages,
                                          ),
                                        );
                                      },
                              child:
                                  state is UploadingImagesState
                                      ? const CircularProgressIndicator(
                                        color: Colors.white,
                                      )
                                      : null,
                            ),
                            const HeightBox(10),
                            Expanded(
                              child:
                                  addEditBlogBloc.uploadedImages.isEmpty
                                      ? const Center(
                                        child: TextWidget.Text(
                                          "No Images Uploaded",
                                        ),
                                      )
                                      : ListView.separated(
                                        itemCount:
                                            addEditBlogBloc
                                                .uploadedImages
                                                .length,
                                        separatorBuilder: (
                                          BuildContext context,
                                          int index,
                                        ) {
                                          // print(
                                          //     "  addEditBlogBloc.uploadedImages.length: ${addEditBlogBloc.uploadedImages.length}");
                                          return const HeightBox(10);
                                        },
                                        itemBuilder: (context, index) {
                                          print(
                                            "  addEditBlogBloc.uploadedImages.length: ${addEditBlogBloc.uploadedImages.length}",
                                          );
                                          return UploadedImageCard(
                                            addEditBlogBloc: addEditBlogBloc,
                                            imageUrl:
                                                addEditBlogBloc
                                                    .uploadedImages[index],
                                          );
                                        },
                                      ),
                            ),
                          ],
                        ),
                      ),
                      const WidthBox(10),
                      Expanded(
                        flex: 4,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            border: Border.all(color: Colors.grey),
                          ),
                          // padding: EdgeInsets.symmetric(horizontal: 5.sp),
                          child: Column(
                            children: [
                              Expanded(
                                child: Scrollbar(
                                  thumbVisibility: true,
                                  controller: scrollController,
                                  child: SingleChildScrollView(
                                    controller: scrollController,
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 5,
                                      ),
                                      child: Column(
                                        children: [
                                          const HeightBox(10),
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                flex: 4,
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    CustomTextField(
                                                      label: "Meta Title",
                                                      controller:
                                                          addEditBlogBloc
                                                              .metaTitleTextEditingController,
                                                    ),

                                                    const HeightBox(10),
                                                    CustomTextField(
                                                      enabled: true,
                                                      label: "Thumbnail Image",
                                                      controller:
                                                          addEditBlogBloc
                                                              .thumbnailTextEditingController,
                                                    ),
                                                    const HeightBox(10),
                                                    TextFieldTags<String>(
                                                      initialTags:
                                                          addEditBlogBloc
                                                              .initialTags,
                                                      textfieldTagsController:
                                                          addEditBlogBloc
                                                              .tagsController,
                                                      textSeparators: const [
                                                        // ' ',
                                                        ',',
                                                        '\n',
                                                      ],
                                                      letterCase:
                                                          LetterCase.normal,
                                                      validator: (String tag) {
                                                        if (addEditBlogBloc
                                                            .tagsController
                                                            .getTags!
                                                            .contains(tag)) {
                                                          return 'You\'ve already entered that';
                                                        }
                                                        return null;
                                                      },
                                                      inputFieldBuilder: (
                                                        context,
                                                        inputFieldValues,
                                                      ) {
                                                        return CustomTextField(
                                                          label: "Tags",
                                                          onTap: () {
                                                            addEditBlogBloc
                                                                .tagsController
                                                                .getFocusNode
                                                                ?.requestFocus();
                                                          },
                                                          controller:
                                                              inputFieldValues
                                                                  .textEditingController,
                                                          focusNode:
                                                              inputFieldValues
                                                                  .focusNode,
                                                          prefix:
                                                              inputFieldValues
                                                                      .tags
                                                                      .isNotEmpty
                                                                  ? SingleChildScrollView(
                                                                    controller:
                                                                        inputFieldValues
                                                                            .tagScrollController,
                                                                    scrollDirection:
                                                                        Axis.vertical,
                                                                    child: Padding(
                                                                      padding: const EdgeInsets.only(
                                                                        top: 8,
                                                                        bottom:
                                                                            8,
                                                                        left: 8,
                                                                      ),
                                                                      child: Wrap(
                                                                        runSpacing:
                                                                            4.0,
                                                                        spacing:
                                                                            4.0,
                                                                        children:
                                                                            inputFieldValues.tags.map((
                                                                              String
                                                                              tag,
                                                                            ) {
                                                                              return Container(
                                                                                decoration: BoxDecoration(
                                                                                  borderRadius: const BorderRadius.all(
                                                                                    Radius.circular(
                                                                                      20.0,
                                                                                    ),
                                                                                  ),
                                                                                  color:
                                                                                      colorsConstants.linkBlue,
                                                                                ),
                                                                                margin: const EdgeInsets.symmetric(
                                                                                  horizontal:
                                                                                      5.0,
                                                                                ),
                                                                                padding: const EdgeInsets.symmetric(
                                                                                  horizontal:
                                                                                      10.0,
                                                                                  vertical:
                                                                                      5.0,
                                                                                ),
                                                                                child: Row(
                                                                                  mainAxisAlignment:
                                                                                      MainAxisAlignment.start,
                                                                                  mainAxisSize:
                                                                                      MainAxisSize.min,
                                                                                  children: [
                                                                                    InkWell(
                                                                                      child: TextWidget.Text(
                                                                                        tag,
                                                                                        style: const TextStyle(
                                                                                          color:
                                                                                              Colors.white,
                                                                                        ),
                                                                                      ),
                                                                                      onTap: () {
                                                                                        //print("$tag selected");
                                                                                      },
                                                                                    ),
                                                                                    const SizedBox(
                                                                                      width:
                                                                                          4.0,
                                                                                    ),
                                                                                    InkWell(
                                                                                      child: Icon(
                                                                                        Icons.cancel,
                                                                                        size:
                                                                                            14.0,
                                                                                        color:
                                                                                            colorsConstants.lightWhite,
                                                                                      ),
                                                                                      onTap: () {
                                                                                        inputFieldValues.onTagRemoved(
                                                                                          tag,
                                                                                        );
                                                                                      },
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              );
                                                                            }).toList(),
                                                                      ),
                                                                    ),
                                                                  )
                                                                  : null,
                                                          onChange:
                                                              inputFieldValues
                                                                  .onTagChanged,
                                                          onSubmit:
                                                              inputFieldValues
                                                                  .onTagSubmitted,
                                                        );
                                                      },
                                                    ),
                                                    const HeightBox(10),
                                                    CustomTextField(
                                                      label: "Permalink",
                                                      controller:
                                                          addEditBlogBloc
                                                              .permaLinkTextEditingController,
                                                    ),
                                                    // HeightBox(20.h),
                                                    const HeightBox(10),
                                                    const TextWidget.Text(
                                                      "Related Blogs",
                                                    ),
                                                    const HeightBox(5),
                                                    MultiDropdown<String>(
                                                      controller:
                                                          addEditBlogBloc
                                                              .multiDropdownController,
                                                      selectedItemBuilder: (
                                                        DropdownItem<String>
                                                        item,
                                                      ) {
                                                        return Chip(
                                                          label: TextWidget.Text(
                                                            item.label,
                                                            style:
                                                                const TextStyle(
                                                                  color:
                                                                      Colors
                                                                          .white,
                                                                ),
                                                          ),
                                                          backgroundColor:
                                                              Colors.blueAccent,
                                                          deleteIcon:
                                                              const Icon(
                                                                Icons.close,
                                                                color:
                                                                    Colors
                                                                        .white,
                                                              ),
                                                          onDeleted: () {
                                                            // print("object");
                                                            // print(item.value);
                                                            // print(item.label);

                                                            /*  BlogModel?
                                                              removedBlogModel =
                                                              addEditBlogBloc
                                                                  .otherBlogs
                                                                  .firstWhereOrNull(
                                                                      (element) =>
                                                                          element
                                                                              .id ==
                                                                          item.value); */
                                                            /* print(
                                                              removedBlogModel !=
                                                                  null); */
                                                            // print("Before");
                                                            // print(addEditBlogBloc
                                                            //     .otherBlogs
                                                            //     .length);
                                                            addEditBlogBloc
                                                                .otherBlogs
                                                                .removeWhere(
                                                                  (element) =>
                                                                      element
                                                                          .id ==
                                                                      item.value,
                                                                );

                                                            // print("After");
                                                            // print(addEditBlogBloc
                                                            //     .otherBlogs
                                                            //     .length);
                                                            addEditBlogBloc
                                                                .multiDropdownController
                                                                .unselectWhere(
                                                                  (option) =>
                                                                      option
                                                                          .value ==
                                                                      item.value,
                                                                );
                                                          },
                                                        );
                                                      },
                                                      items:
                                                          (widget.allBlogs ??
                                                                  [])
                                                              .map(
                                                                (
                                                                  blog,
                                                                ) => DropdownItem<
                                                                  String
                                                                >(
                                                                  value:
                                                                      blog.id
                                                                          .toString(),
                                                                  label:
                                                                      blog.metaTitle ??
                                                                      '',
                                                                ),
                                                              )
                                                              .toList(),
                                                      chipDecoration:
                                                          const ChipDecoration(
                                                            backgroundColor:
                                                                Colors
                                                                    .blueAccent,
                                                          ),
                                                      onSelectionChange: (
                                                        selectedIds,
                                                      ) {
                                                        // setState(() {
                                                        addEditBlogBloc
                                                            .otherBlogs
                                                            .clear();
                                                        for (var id
                                                            in selectedIds) {
                                                          final blog = widget
                                                              .allBlogs
                                                              ?.firstWhere(
                                                                (b) =>
                                                                    b.id.toString() ==
                                                                    id,
                                                              );
                                                          if (blog != null)
                                                            addEditBlogBloc
                                                                .otherBlogs
                                                                .add(blog);
                                                        }
                                                        // });
                                                      },
                                                      fieldDecoration: FieldDecoration(
                                                        hintText:
                                                            "Select related blogs",
                                                        hintStyle:
                                                            const TextStyle(
                                                              color:
                                                                  Colors.black,
                                                            ),
                                                        borderRadius: 5,
                                                        backgroundColor:
                                                            Colors.transparent,
                                                        border: OutlineInputBorder(
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                5,
                                                              ),
                                                          borderSide:
                                                              const BorderSide(
                                                                color:
                                                                    Colors
                                                                        .black,
                                                                width: 1,
                                                              ),
                                                        ),
                                                        suffixIcon: const Icon(
                                                          Icons.arrow_drop_down,
                                                        ),
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 12,
                                                              vertical: 8,
                                                            ),
                                                        showClearIcon: true,
                                                      ),
                                                      dropdownDecoration:
                                                          const DropdownDecoration(
                                                            borderRadius:
                                                                BorderRadius.all(
                                                                  Radius.circular(
                                                                    5,
                                                                  ),
                                                                ),
                                                            maxHeight: 300,
                                                          ),
                                                      itemSeparator: HeightBox(
                                                        16,
                                                      ),
                                                      searchEnabled: false,
                                                    ),
                                                    /*    MultiSelectDropDown(
                                                      borderRadius: 5,
                                                      hintStyle:
                                                          const TextStyle(
                                                            color: Colors.black,
                                                          ),
                                                      borderColor: Colors.black,
                                                      borderWidth: 1,
                                                      fieldBackgroundColor:
                                                          Colors.transparent,
                                                      hint:
                                                          "Select related blogs",
                                                      // padding: EdgeInsets.symmetric(
                                                      //     vertical: 1.sp,
                                                      //     horizontal: 1.sp),
                                                      controller:
                                                          addEditBlogBloc
                                                              .otherBlogsSelectController,
                                                      onOptionSelected: (
                                                        options,
                                                      ) {
                                                        debugPrint(
                                                          options.toString(),
                                                        );
                                                      },
                                                      onOptionRemoved: (
                                                        index,
                                                        option,
                                                      ) {
                                                        print("object");
                                                        BlogsModel?
                                                        removedBlogModel =
                                                            addEditBlogBloc
                                                                .otherBlogs
                                                                .firstWhereOrNull(
                                                                  (element) =>
                                                                      element
                                                                          .id ==
                                                                      option
                                                                          .value,
                                                                );

                                                        addEditBlogBloc
                                                            .otherBlogs
                                                            .remove(
                                                              removedBlogModel,
                                                            );
                                                      },
                                                      options:
                                                          widget.allBlogs !=
                                                                  null
                                                              ? widget.allBlogs!.map((
                                                                BlogsModel
                                                                blogModel,
                                                              ) {
                                                                return ValueItem(
                                                                  label:
                                                                      blogModel
                                                                          .metaTitle ??
                                                                      "",
                                                                  value:
                                                                      blogModel
                                                                          .id,
                                                                );
                                                              }).toList()
                                                              : [],
                                                      selectionType:
                                                          SelectionType.multi,
                                                      chipConfig:
                                                          const ChipConfig(
                                                            wrapType:
                                                                WrapType.wrap,
                                                          ),
                                                      optionSeparator:
                                                          const HeightBox(2),
                                                      dropdownHeight: 300,
                                                      dropdownBorderRadius: 5,
                                                      optionBuilder: (
                                                        ctx,
                                                        item,
                                                        selected,
                                                      ) {
                                                        return Container(
                                                          padding:
                                                              const EdgeInsets.symmetric(
                                                                horizontal: 1,
                                                                vertical: 2,
                                                              ),
                                                          child:
                                                              TextWidget.Text(
                                                                item.label,
                                                              ),
                                                        );
                                                      },
                                                      selectedOptionIcon:
                                                          const Icon(
                                                            Icons.check_circle,
                                                          ),
                                                    ), */
                                                    const HeightBox(10),
                                                    const Row(
                                                      children: [
                                                        TextWidget.Text(
                                                          "Content",
                                                        ),
                                                        WidthBox(2),
                                                        // InkWell(
                                                        //   onTap: () {
                                                        //     showDialog(
                                                        //       context: context,
                                                        //       builder: (BuildContext
                                                        //           context) {
                                                        //         return const MarkdownTips();
                                                        //       },
                                                        //     );
                                                        //   },
                                                        //   child: const Icon(
                                                        //       Icons.info_rounded),
                                                        // ),
                                                      ],
                                                    ),
                                                    const HeightBox(1),
                                                    Container(
                                                      margin:
                                                          const EdgeInsets.symmetric(
                                                            horizontal: 5.0,
                                                          ),
                                                      padding:
                                                          const EdgeInsets.symmetric(
                                                            horizontal: 10.0,
                                                            vertical: 5.0,
                                                          ),
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              7,
                                                            ),
                                                        border: Border.all(
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                      // color: Colors.black,
                                                      width: 1000,
                                                      child: Row(
                                                        children: [
                                                          Expanded(
                                                            child: SingleChildScrollView(
                                                              child: HtmlEditor(
                                                                htmlToolbarOptions: const HtmlToolbarOptions(
                                                                  toolbarType:
                                                                      ToolbarType
                                                                          .nativeGrid,
                                                                  renderSeparatorWidget:
                                                                      true,
                                                                  renderBorder:
                                                                      false,
                                                                  initiallyExpanded:
                                                                      false,
                                                                ),
                                                                controller:
                                                                    hcontroller,

                                                                htmlEditorOptions: HtmlEditorOptions(
                                                                  initialText:
                                                                      widget
                                                                          .blogModel
                                                                          ?.htmlString,
                                                                  androidUseHybridComposition:
                                                                      true,
                                                                  shouldEnsureVisible:
                                                                      true,
                                                                  spellCheck:
                                                                      true,
                                                                  autoAdjustHeight:
                                                                      true,
                                                                  adjustHeightForKeyboard:
                                                                      true,
                                                                  hint:
                                                                      "Draft your content here...",
                                                                ),
                                                                // htmlToolbarOptions:
                                                                //     const HtmlToolbarOptions(
                                                                //   toolbarPosition:
                                                                //       ToolbarPosition
                                                                //           .aboveEditor,
                                                                // ),
                                                                otherOptions:
                                                                    const OtherOptions(
                                                                      height:
                                                                          400,
                                                                    ),
                                                                callbacks: Callbacks(
                                                                  onChangeContent: (
                                                                    p0,
                                                                  ) {
                                                                    print(p0);
                                                                    webviewController.loadContent(
                                                                      p0 ?? '',
                                                                      sourceType:
                                                                          SourceType
                                                                              .html,
                                                                    );
                                                                    addEditBlogBloc
                                                                        .markdownTextEditingController
                                                                        .text = p0 ??
                                                                        "";
                                                                    addEditBlogBloc
                                                                        .markdownNotifier
                                                                        .value = p0 ??
                                                                        "";
                                                                  },
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          // ElevatedButton(
                                                          //     onPressed: () async {
                                                          //       print(
                                                          //           await hcontroller
                                                          //               .getText());
                                                          //     },
                                                          //     child: const HintText(
                                                          //         hintText:
                                                          //             "Print"))
                                                        ],
                                                      ),
                                                    ),
                                                    // CustomTextField(
                                                    //   maxLine: 20,
                                                    //   controller: addEditBlogBloc
                                                    //       .markdownTextEditingController,
                                                    //   onChange: (value) {
                                                    //     addEditBlogBloc
                                                    //         .markdownNotifier
                                                    //         .value = value;
                                                    //   },
                                                    // ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const HeightBox(10),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 5,
                                ),
                                child: PrimaryButton(
                                  title: "Submit Blog",
                                  onTap:
                                      state is AddingBlogState
                                          ? () {}
                                          : () {
                                            addEditBlogBloc.add(
                                              AddBlogEvent(
                                                blogModel: BlogsModel(
                                                  id: widget.blogModel?.id,
                                                  metaTitle:
                                                      addEditBlogBloc
                                                          .metaTitleTextEditingController
                                                          .text,
                                                  htmlString: markdownToHtml(
                                                    addEditBlogBloc
                                                        .markdownNotifier
                                                        .value,
                                                  ),
                                                  markdown:
                                                      addEditBlogBloc
                                                          .markdownNotifier
                                                          .value,
                                                  permalink:
                                                      addEditBlogBloc
                                                          .permaLinkTextEditingController
                                                          .text,
                                                  tags:
                                                      addEditBlogBloc
                                                          .tagsController
                                                          .getTags,
                                                  thumbnail:
                                                      addEditBlogBloc
                                                          .thumbnailTextEditingController
                                                          .text,
                                                  isVisible: true,
                                                ),
                                              ),
                                            );
                                          },
                                  child:
                                      state is AddingBlogState
                                          ? const CircularProgressIndicator(
                                            color: Colors.white,
                                          )
                                          : null,
                                ),
                              ),
                              const HeightBox(10),
                            ],
                          ),
                        ),
                      ),
                      const WidthBox(2),
                      SizedBox(
                        width: 500,
                        // flex: 3,
                        child: Column(
                          children: [
                            Expanded(
                              child: ValueListenableBuilder(
                                valueListenable:
                                    addEditBlogBloc.markdownNotifier,
                                builder: (context, value, _) {
                                  return /*  addEditBlogBloc
                                        .markdownNotifier.value.isEmpty
                                    ? const Center(
                                        child: TextWidget.Text(
                                            "No markdown added"),
                                      )
                                     :*/ WebViewX(
                                    initialContent: "No Content",
                                    initialSourceType: SourceType.html,
                                    onWebViewCreated:
                                        (controller) =>
                                            webviewController = controller,
                                    width: 500,
                                    height: MediaQuery.sizeOf(context).height,
                                  );
                                  /*  Markdown(
                                        data: addEditBlogBloc
                                            .markdownTextEditingController.text,
                                        extensionSet: ExtensionSet.gitHubWeb,
                                        selectable: true,
                                        styleSheet: MarkdownStyleSheet(
                                          h1: const TextStyle(
                                            fontWeight: FontWeight.w900,
                                          ),
                                        ),
                                      ) */
                                  // ;
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}

class UploadedImageCard extends StatefulWidget {
  const UploadedImageCard({
    super.key,
    required this.addEditBlogBloc,
    required this.imageUrl,
  });

  final AddEditBlogBloc addEditBlogBloc;
  final String imageUrl;

  @override
  State<UploadedImageCard> createState() => _UploadedImageCardState();
}

class _UploadedImageCardState extends State<UploadedImageCard> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        await Clipboard.setData(ClipboardData(text: widget.imageUrl));
      },
      child: MouseRegion(
        onEnter: (event) {
          setState(() {
            isHovered = true;
          });
        },
        onExit: (event) {
          setState(() {
            isHovered = false;
          });
        },
        child: AspectRatio(
          aspectRatio: 1,
          child: Stack(
            fit: StackFit.expand,
            children: [
              SizedBox(child: Image.network(widget.imageUrl)),
              AnimatedOpacity(
                opacity: isHovered ? 1 : 0,
                duration: Duration(milliseconds: isHovered ? 250 : 0),
                child: Container(
                  color: Colors.black38,
                  child: const Icon(CupertinoIcons.link, color: Colors.white),
                ),
              ),
            ],
          ),
        ),
        // child: Column(
        //   children: [
        //     HeightBox(5.h),
        //     Image.network(addEditBlogBloc
        //         .uploadedImages[index]),
        //     HeightBox(10.h),
        //     ElevatedButton(
        //       onPressed: () async {
        //         await Clipboard.setData(
        //           ClipboardData(
        //               text: addEditBlogBloc
        //                       .uploadedImages[
        //                   index]),
        //         );
        //       },
        //       child: const TextWidget.Text(
        //           "Copy Link"),
        //     ),
        //     HeightBox(5.h),
        //   ],
        // ),
      ),
    );
  }
}
