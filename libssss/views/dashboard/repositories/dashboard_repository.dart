import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../models/blogsmodel.dart';

class DashboardRepository {
  Future<List<BlogsModel>> fetchBlogs() async {
    QuerySnapshot<Map<String, dynamic>> allBlogsRefs =
        await FirebaseFirestore.instance.collection("Blogs").get();

    List<BlogsModel> blogs = [];

    for (var blogDoc in allBlogsRefs.docs) {
      final data = blogDoc.data();
      BlogsModel blogModel = BlogsModel.fromMap(blogDoc.data())
        ..id = blogDoc.id;
      blogs.add(blogModel);
    }

    return blogs;
  }

  Future<void> deleteBlog({required BlogsModel blogModel}) async {
    await FirebaseFirestore.instance
        .collection("Blogs")
        .doc(blogModel.id)
        .delete();
  }

  Future<List<BlogsModel>> showHideBlog({required BlogsModel blogModel}) async {
    // blogModel.isVisible = !blogModel.isVisible!;
    await FirebaseFirestore.instance
        .collection("Blogs")
        .doc(blogModel.id)
        .update(blogModel.toMap());

    return await fetchBlogs();
  }
}
