import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../configurations/app_configurations.dart';
import '../../../models/blogsmodel.dart';
import '../../../routers/extensions.dart';
import '../../../routers/navigation_helper.dart';
import '../../../utils/utils.dart';
import '../../widgets/height_box.dart';
import '../../widgets/primary_button.dart';
import '../../widgets/width_box.dart';
import '../bloc/dashboard_bloc.dart';
import '../repositories/dashboard_repository.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          DashboardBloc(dashboardRepository: DashboardRepository())
            ..add(LoadDashboardData()),
      child: Builder(
        builder: (context) {
          DashboardBloc dashboardBloc = context.read<DashboardBloc>();

          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              elevation: 5,
              shadowColor: Colors.black12,
              toolbarHeight: 16.sp,
              leadingWidth: 30.sp,
              backgroundColor: Colors.blue,
              title: Padding(
                padding: const EdgeInsets.only(left: 60),
                child: Image.asset(
                  height: 70,
                  assetsConstants.logo,
                  // fit: BoxFit.fill,
                  color: Colors.white,
                ),
              ),
              // leading:
              actions: [
                PrimaryButton(
                  onTap: () {
                    moveToAddEditBlogScreen(
                      context: context,
                      allBlogs: dashboardBloc.state.allBlogs,
                      revertCallback: (_) {
                        dashboardBloc.add(LoadDashboardData());
                      },
                    );
                  },
                  title: "Add Blog",
                  backgroundColor: Colors.white,
                  titleStyle: TextStyle(fontSize: 3.sp, color: Colors.black),
                  width: 20.w,
                  margin: EdgeInsets.symmetric(vertical: 4.sp),
                  padding: EdgeInsets.symmetric(vertical: 1.sp),
                ),
                WidthBox(5.w),
                InkWell(
                  onTap: () {
                    FirebaseAuth.instance.signOut();
                    moveToLoginScreen(
                        context: context,
                        operation: NavigationOperations.pushReplacementNamed);
                  },
                  child: const Icon(
                    Icons.logout_rounded,
                    color: Colors.white,
                  ),
                ),
                WidthBox(5.w),
              ],
            ),
            body: BlocConsumer<DashboardBloc, DashboardState>(
              listener: (context, state) {
                if (state is DashboardErrorState) {
                  Utils.showSnackbar(
                      context: context,
                      message: state.errorData?.data["message"]);
                } else if (state is BlogDeletedState) {
                  Navigator.pop(context);
                  context.read<DashboardBloc>().add(LoadDashboardData());
                }
              },
              builder: (context, state) {
                if (state is LoadingDashboardDataState) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: Colors.blue,
                    ),
                  );
                }

                if (state is DashboardDataLoadedState &&
                    state.allBlogs.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "No data found",
                          style: TextStyle(fontSize: 5.sp),
                        ),
                        PrimaryButton(
                          onTap: () {
                            moveToAddEditBlogScreen(
                              context: context,
                              allBlogs: dashboardBloc.state.allBlogs,
                              revertCallback: (_) {
                                dashboardBloc.add(LoadDashboardData());
                              },
                            );
                          },
                          title: "Add Blog",
                          titleStyle:
                              TextStyle(fontSize: 4.sp, color: Colors.white),
                          width: 24.w,
                          margin: EdgeInsets.symmetric(vertical: 5.sp),
                          padding: EdgeInsets.symmetric(vertical: 2.sp),
                        ),
                      ],
                    ),
                  );
                }

                return SizedBox();

                // return ResponsiveGridList(
                //   horizontalGridSpacing: 8.sp,
                //   verticalGridSpacing: 8.sp,
                //   horizontalGridMargin: 10.sp,
                //   verticalGridMargin: 10.sp,
                //   minItemWidth: 20.sp,
                //   minItemsPerRow: 2,
                //   maxItemsPerRow: 5,
                //   listViewBuilderOptions: ListViewBuilderOptions(
                //       shrinkWrap:
                //           true), // Options that are getting passed to the ListView.builder() function
                //   children: state.allBlogs.map((BlogModel blogModel) {
                //     // print("${blogModel.thumbnail}...........blogmodel");
                //     return Card(
                //       elevation: 2,
                //       clipBehavior: Clip.antiAlias,
                //       color: Colors.white,
                //       surfaceTintColor: Colors.white,
                //       margin: EdgeInsets.zero,
                //       child: Stack(
                //         children: [
                //           InkWell(
                //             onTap: () {
                //               moveToAddEditBlogScreen(
                //                 context: context,
                //                 blogModel: blogModel,
                //                 allBlogs: dashboardBloc.state.allBlogs,
                //                 revertCallback: (_) {
                //                   dashboardBloc.add(LoadDashboardData());
                //                 },
                //               );
                //             },
                //             onLongPress: () {
                //               showDialog(
                //                 context: context,
                //                 builder: (context) {
                //                   return DeletePopup(
                //                     dashboardBloc: dashboardBloc,
                //                     blogModel: blogModel,
                //                   );
                //                 },
                //               );
                //             },
                //             hoverColor: Colors.transparent,
                //             splashColor: Colors.transparent,
                //             child: Column(
                //               mainAxisAlignment: MainAxisAlignment.start,
                //               mainAxisSize: MainAxisSize.min,
                //               children: [
                //                 Stack(
                //                   children: [
                //                     Image.network(
                //                       blogModel.thumbnail ?? "",
                //                       height: 50.sp,
                //                       width: 70.sp,
                //                       fit: BoxFit.cover,
                //                       // errorBuilder:
                //                       //     (context, error, stackTrace) {
                //                       //   return Center(
                //                       //     child: Icon(Icons.error),
                //                       //   );
                //                       // },
                //                     ),
                //                     Container(
                //                       height: 50.sp,
                //                       width: 70.sp,
                //                       decoration: BoxDecoration(
                //                         gradient: LinearGradient(
                //                           begin: Alignment.topCenter,
                //                           end: Alignment.bottomCenter,
                //                           colors: [
                //                             Colors.black.withOpacity(0.3),
                //                             Colors.black.withOpacity(0.0)
                //                           ],
                //                         ),
                //                       ),
                //                     )
                //                   ],
                //                 ),
                //                 HeightBox(2.sp),
                //                 Padding(
                //                   padding:
                //                       EdgeInsets.symmetric(horizontal: 2.w),
                //                   child: Text(
                //                     softWrap: true,
                //                     blogModel.metaTitle ?? "",
                //                     maxLines: 3,
                //                     overflow: TextOverflow.ellipsis,
                //                     style: TextStyle(fontSize: 3.sp),
                //                   ),
                //                 ),
                //                 HeightBox(2.sp),
                //               ],
                //             ),
                //           ),
                //           Positioned(
                //             right: 1.5.sp,
                //             top: 1.sp,
                //             child: InkWell(
                //               onTap: () {
                //                 dashboardBloc.add(
                //                   ShowHideBlogEvent(
                //                     blogModel: blogModel,
                //                   ),
                //                 );
                //               },
                //               child: Icon(
                //                 blogModel.isVisible == true
                //                     ? Icons.visibility_rounded
                //                     : Icons.visibility_off_rounded,
                //                 color: Colors.white,
                //                 size: 5.sp,
                //               ),
                //             ),
                //           )
                //         ],
                //       ),
                //     );
                //   }).toList(), // The list of widgets in the list
                // );
              },
            ),
          );
        },
      ),
    );
  }
}

class DeletePopup extends StatelessWidget {
  const DeletePopup({
    super.key,
    required this.dashboardBloc,
    required this.blogModel,
  });

  final DashboardBloc dashboardBloc;
  final BlogsModel blogModel;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      actions:
          //  [],
          // children:
          [
        Padding(
          padding: const EdgeInsets.only(top: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Are you sure you want to delete?",
                style: TextStyle(fontSize: 5.sp),
              ),
              HeightBox(5.sp),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PrimaryButton(
                    title: "Delete",
                    width: 30.w,
                    height: 40.h,
                    backgroundColor: colorsConstants.errorRed,
                    onTap: () async {
                      dashboardBloc.add(
                        DeleteBlogEvent(
                          blogModel: blogModel,
                        ),
                      );
                    },
                  ),
                  WidthBox(4.sp),
                  PrimaryButton(
                    title: "Cancel",
                    width: 30.w,
                    height: 40.h,
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                ],
              )
            ],
          ),
        ),
      ],
      // child:
    );
  }
}
