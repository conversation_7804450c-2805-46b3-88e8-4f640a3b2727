// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:go_router/go_router.dart';
// import 'package:luxor_admin/controller/homectrl.dart';
// import 'package:luxor_admin/shared/methods.dart';
// import '../../shared/firebase.dart';
// import '../../shared/image_picker.dart';

// class GalleryPage extends StatefulWidget {
//   const GalleryPage({super.key});

//   @override
//   State<GalleryPage> createState() => _GalleryPageState();
// }

// class _GalleryPageState extends State<GalleryPage> {
//   bool onSubmitLoad = false;
//   int? selectedContent;

//   TextEditingController titlectrl = TextEditingController();
//   TextEditingController videotitlectrl = TextEditingController();
//   TextEditingController imageUrlctrl = TextEditingController();
//   TextEditingController videoUrlctrl = TextEditingController();
//   TextEditingController descctrl = TextEditingController();
//   TextEditingController videodesctrl = TextEditingController();
//   TextEditingController thumbnailctrl = TextEditingController();
//   TextEditingController videothumbnailctrl = TextEditingController();

//   SelectedImage? imageFileList;

//   List<String> imageurl = [];

//   Future<String?> uploadImage(SelectedImage imageFile) async {
//     try {
//       final path =
//           "Images/${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}";
//       final imageRef = FBStorage.fbstore.ref().child(path);

//       final task = await imageRef.putData(imageFile.uInt8List);
//       var downloadurl = await task.ref.getDownloadURL();
//       imageurl.add(downloadurl);

//       return await task.ref.getDownloadURL();
//     } on Exception catch (e) {
//       debugPrint(e.toString());
//       return null;
//     }
//   }

//   bool videoIsChecked = false;
//   bool imageIsChecked = false;

//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<HomeCtrl>(builder: (ctrl) {
//       return SingleChildScrollView(
//         padding: const EdgeInsets.all(40),
//         child: Column(
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 const Text("Gallery",
//                     style: TextStyle(
//                       fontSize: 28,
//                     )),
//                 SizedBox(
//                   width: 300,
//                   child: DropdownButtonHideUnderline(
//                     child: DropdownButtonFormField(
//                       decoration: InputDecoration(
//                           hintText: "Add Your Content",
//                           border: OutlineInputBorder(
//                               borderRadius: BorderRadius.circular(10))),
//                       items: [
//                         DropdownMenuItem(
//                           enabled: true,
//                           value: 0,
//                           child: const Text("Image"),
//                           onTap: () => addImageDialog(context),
//                         ),
//                         DropdownMenuItem(
//                           enabled: true,
//                           value: 2,
//                           child: const Text("Video"),
//                           onTap: () => addVideoDialog(context),
//                         ),
//                       ],
//                       onChanged: (value) {
//                         setState(() {
//                           selectedContent = value;
//                         });
//                       },
//                     ),
//                   ),
//                 )
//               ],
//             ),
//             // SizedBox(height: 40),
//           ],
//         ),
//       );
//     });
//   }

//   Future<dynamic> addVideoDialog(BuildContext context) {
//     return showDialog(
//       context: context,
//       builder: (context) => StatefulBuilder(builder: (context, setState2) {
//         return AlertDialog(
//           backgroundColor: Colors.white,
//           title: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               const Text("Add Video"),
//               IconButton(
//                 onPressed: () {
//                   context.pop();
//                 },
//                 icon: const Icon(Icons.close),
//               )
//             ],
//           ),
//           content: SizedBox(
//             width: 600,
//             height: 300,
//             child: Column(
//               children: [
//                 TextFormField(
//                   controller: videotitlectrl,
//                   decoration: const InputDecoration(
//                       border: OutlineInputBorder(
//                           borderSide: BorderSide(color: Colors.black),
//                           borderRadius: BorderRadius.all(Radius.circular(10))),
//                       labelText: 'Title'),
//                 ),
//                 const SizedBox(height: 12),
//                 TextFormField(
//                   controller: videodesctrl,
//                   decoration: const InputDecoration(
//                       border: OutlineInputBorder(
//                           borderSide: BorderSide(color: Colors.black),
//                           borderRadius: BorderRadius.all(Radius.circular(10))),
//                       labelText: 'Description'),
//                 ),
//                 const SizedBox(height: 12),
//                 TextFormField(
//                   controller: videoUrlctrl,
//                   decoration: const InputDecoration(
//                       border: OutlineInputBorder(
//                           borderSide: BorderSide(color: Colors.black),
//                           borderRadius: BorderRadius.all(Radius.circular(10))),
//                       labelText: 'Video URL'),
//                 ),
//                 const SizedBox(height: 12),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     const Text("Special"),
//                     Checkbox(
//                       value: videoIsChecked,
//                       onChanged: (bool? value) {
//                         setState2(() {
//                           videoIsChecked = value!;
//                         });
//                       },
//                     )
//                   ],
//                 ),
//                 const SizedBox(height: 12),
//                 onSubmitLoad
//                     ? const Center(
//                         child: CircularProgressIndicator(
//                             color: Colors.red, strokeWidth: 3.5))
//                     : ElevatedButton(
//                         style: ButtonStyle(
//                             backgroundColor:
//                                 const WidgetStatePropertyAll(Colors.red),
//                             padding: const WidgetStatePropertyAll(
//                                 EdgeInsets.symmetric(
//                                     horizontal: 20, vertical: 20)),
//                             side: const WidgetStatePropertyAll(
//                                 BorderSide(color: Colors.transparent)),
//                             shape:
//                                 WidgetStatePropertyAll(RoundedRectangleBorder(
//                               borderRadius: BorderRadius.circular(5),
//                             ))),
//                         onPressed: () async {
//                           onSubmitLoad = true;
//                           try {
//                             setState2(() {
//                               onSubmitLoad = true;
//                             });
//                             FBFireStore.gallery.add({
//                               'title': videotitlectrl.text,
//                               'thumbnail': videothumbnailctrl.text ?? "-",
//                               'imageUrl': videoUrlctrl.text,
//                               'hasImage': false,
//                               'desc': videodesctrl.text,
//                               'isSpecial': videoIsChecked
//                             });
//                             context.pop();
//                             showAppSnackBar("Video Content Added Successfully");
//                             print("video data updated");
//                           } catch (e) {
//                             print("error uploading video");
//                           }
//                         },
//                         child: const Text("Submit Video",
//                             style:
//                                 TextStyle(color: Colors.white, fontSize: 16))),
//               ],
//             ),
//           ),
//         );
//       }),
//     );
//   }

//   Future<dynamic> addImageDialog(BuildContext context) {
//     imageFileList = null;
//     onSubmitLoad = false;
//     return showDialog(
//         context: context,
//         builder: (context) => StatefulBuilder(builder: (context, setState2) {
//               return AlertDialog(
//                 backgroundColor: Colors.white,
//                 title: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     const Text("Add Image"),
//                     IconButton(
//                       onPressed: () {
//                         context.pop();
//                       },
//                       icon: const Icon(Icons.close),
//                     )
//                   ],
//                 ),
//                 content: SizedBox(
//                     width: 600,
//                     // height: MediaQuery.sizeOf(context).height,
//                     height: 350,
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.spaceAround,
//                       children: [
//                         TextFormField(
//                           controller: titlectrl,
//                           decoration: const InputDecoration(
//                               border: OutlineInputBorder(
//                                   borderSide: BorderSide(color: Colors.black),
//                                   borderRadius:
//                                       BorderRadius.all(Radius.circular(10))),
//                               labelText: 'Title'),
//                         ),
//                         const SizedBox(height: 12),
//                         TextFormField(
//                           controller: descctrl,
//                           decoration: const InputDecoration(
//                               border: OutlineInputBorder(
//                                   borderSide: BorderSide(color: Colors.black),
//                                   borderRadius:
//                                       BorderRadius.all(Radius.circular(10))),
//                               labelText: 'Description'),
//                         ),
//                         const SizedBox(height: 12),
//                         TextFormField(
//                           controller: thumbnailctrl,
//                           decoration: const InputDecoration(
//                               border: OutlineInputBorder(
//                                   borderSide: BorderSide(color: Colors.black),
//                                   borderRadius:
//                                       BorderRadius.all(Radius.circular(10))),
//                               labelText: 'Thumbnail'),
//                         ),
//                         const SizedBox(height: 12),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             const Text("Special"),
//                             Checkbox(
//                               value: imageIsChecked,
//                               onChanged: (bool? value) {
//                                 setState2(() {
//                                   imageIsChecked = value!;
//                                 });
//                               },
//                             )
//                           ],
//                         ),
//                         const SizedBox(height: 12),
//                         imageFileList != null
//                             ? Stack(
//                                 alignment: Alignment.topRight,
//                                 children: [
//                                   Image.memory(
//                                     imageFileList!.uInt8List,
//                                     height: 50,
//                                     width: 50,
//                                     fit: BoxFit.cover,
//                                   ),
//                                   IconButton(
//                                     icon: const Icon(
//                                       Icons.cancel,
//                                       color: Colors.red,
//                                     ),
//                                     onPressed: () async {
//                                       setState2(() {
//                                         imageFileList = null;
//                                       });

//                                       try {
//                                         if (imageFileList != null) {
//                                           final path =
//                                               "Images/${DateTime.now().millisecondsSinceEpoch}.${imageFileList!.extention}";
//                                           final imageRef = FBStorage.fbstore
//                                               .ref()
//                                               .child(path);
//                                           await imageRef.delete();
//                                           showAppSnackBar(
//                                               "Image removed successfully.");
//                                         }
//                                       } catch (e) {
//                                         showAppSnackBar(
//                                             "Error removing image.");
//                                       }
//                                     },
//                                   ),
//                                 ],
//                               )
//                             : ElevatedButton(
//                                 onPressed: () async {
//                                   imageFileList = await ImagePickerService()
//                                       .pickImageNew(context,
//                                           useCompressor: true);
//                                   setState2(() {});
//                                 },
//                                 child: const Icon(CupertinoIcons
//                                     .photo_fill_on_rectangle_fill),
//                               ),
//                         const SizedBox(height: 12),
//                         onSubmitLoad
//                             ? const Center(
//                                 child: CircularProgressIndicator(
//                                     color: Colors.red, strokeWidth: 3.5))
//                             : ElevatedButton(
//                                 style: ButtonStyle(
//                                     backgroundColor:
//                                         const WidgetStatePropertyAll(
//                                             Colors.red),
//                                     padding: const WidgetStatePropertyAll(
//                                         EdgeInsets.symmetric(
//                                             horizontal: 20, vertical: 20)),
//                                     side: const WidgetStatePropertyAll(
//                                         BorderSide(color: Colors.transparent)),
//                                     shape: WidgetStatePropertyAll(
//                                         RoundedRectangleBorder(
//                                       borderRadius: BorderRadius.circular(5),
//                                     ))),
//                                 onPressed: () async {
//                                   onSubmitLoad = true;
//                                   try {
//                                     setState2(() {
//                                       onSubmitLoad = true;
//                                     });
//                                     final finalImageUrl =
//                                         await uploadImage(imageFileList!);

//                                     FBFireStore.gallery.add({
//                                       'title': titlectrl.text.isNotEmpty
//                                           ? titlectrl.text
//                                           : "-",
//                                       'imageUrl': finalImageUrl,
//                                       'desc': descctrl.text.isNotEmpty
//                                           ? descctrl.text
//                                           : "-",
//                                       'hasImage': true,
//                                       'thumbnail': thumbnailctrl.text.isNotEmpty
//                                           ? thumbnailctrl.text
//                                           : "-",
//                                       'isSpecial': imageIsChecked
//                                     });
//                                     context.pop();

//                                     print(titlectrl.text);
//                                     print(imageUrlctrl.text);
//                                     print(descctrl.text);
//                                     print(thumbnailctrl.text);
//                                     print("image content added");
//                                   } catch (e) {
//                                     print("Error posting image");
//                                   }
//                                 },
//                                 child: const Text("Submit Image",
//                                     style: TextStyle(
//                                         color: Colors.white, fontSize: 16))),
//                         const SizedBox(width: 15),
//                         const SizedBox(
//                           child: Column(
//                             children: [
//                               // ...List.generate(1, (index) => )
//                             ],
//                           ),
//                         )
//                       ],
//                     )),
//               );
//             }));
//   }
// }
