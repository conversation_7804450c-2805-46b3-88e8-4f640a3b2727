// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:go_router/go_router.dart';

// import '../../controller/homectrl.dart';
// import '../../shared/router.dart';

// class DashboardTile extends StatelessWidget {
//   const DashboardTile(
//       {super.key,
//       required this.icon,
//       required this.textName,
//       this.isSelected = false,
//       this.tab = false,
//       this.scafKey,
//       required this.route});

//   final IconData icon;
//   final String textName;
//   final bool isSelected;
//   final bool tab;
//   final GlobalKey<ScaffoldState>? scafKey;
//   final String route;

//   @override
//   Widget build(BuildContext context) {
//     final selected = appRouter.routeInformationProvider.value.uri.path == route;

//     return InkWell(
//       borderRadius: BorderRadius.circular(8),
//       onTap: () {
//         context.go(route);
//         Get.find<HomeCtrl>().update();
//         scafKey?.currentState?.closeDrawer();
//       },
//       child: Container(
//         decoration: BoxDecoration(
//             color: selected
//                 ? const Color(0xff68b38c

//                     // 00a650

//                     // b6e3cd
//                     // 0xff688b42

//                     )
//                 : Colors.transparent,
//             // color: selected ? themeColor.withOpacity(0.1) : Colors.transparent,
//             borderRadius: BorderRadius.circular(8)),
//         padding: const EdgeInsets.all(10),
//         width: 230,
//         child: tab ? _column(selected) : _row(selected),
//       ),
//     );
//   }

// // F O R   O T H E R
//   Row _row(bool selected) {
//     return Row(
//       children: [
//         _icon(selected),
//         const SizedBox(width: 15),
//         _text(selected),
//       ],
//     );
//   }

// // F O R  T A B L E T
//   Column _column(bool selected) {
//     return Column(
//       children: [
//         _icon(selected),
//         const SizedBox(height: 5),
//         _text(selected),
//       ],
//     );
//   }

//   Text _text(bool selected) {
//     return Text(textName,
//         textAlign: TextAlign.center,
//         style: selected
//             ? TextStyle(
//                 letterSpacing: 1,
//                 fontWeight: selected ? FontWeight.w500 : FontWeight.w400,
//                 color: selected ? Colors.white : Colors.black)
//             : null);
//   }

//   Icon _icon(bool selected) =>
//       Icon(icon, color: selected ? Colors.white : Colors.black);
//   // Icon(icon, color: selected ? themeColor.withOpacity(0.85) : null);
// }




// /*
//   Text _text(bool selected, [double? size]) {
//     return Text(
//       textName,
//       style: selected
//           ? TextStyle(
//               fontWeight: FontWeight.w600,
//               color: Color(0xff4676fe),
//               fontSize: size)
//           : null,
//     );
//   }
// }

//         child: Row(
//           children: [
//             Icon(icon),
//             SizedBox(width: 15),
//             Text(textName),
//           ],
//         ),
//         */