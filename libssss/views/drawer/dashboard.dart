// import 'package:flutter/material.dart';

// import 'dashboard_drawer.dart';

// class DashboardScreen extends StatelessWidget {
//   const DashboardScreen({super.key, required this.child});
//   final Widget child;

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.grey.shade100,
//       body: Row(
//         children: [
//           const DashboardDrawer(),
//           Expanded(child: child),
//         ],
//       ),
//     );
//   }
// }
