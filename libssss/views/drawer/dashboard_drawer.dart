// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:go_router/go_router.dart';
// import '../../controller/homectrl.dart';
// import '../../shared/firebase.dart';
// import '../../shared/router.dart';
// import 'db_drawer_tile.dart';

// class DashboardDrawer extends StatelessWidget {
//   const DashboardDrawer({
//     super.key,
//     this.isTablet = false,
//     this.scafKey,
//   });

//   final bool isTablet;
//   final GlobalKey<ScaffoldState>? scafKey;

//   @override
//   Widget build(BuildContext context) {
//     return Material(
//       color: Colors.white,
//       // Color(0xfff7f7f7),
//       child: GetBuilder<HomeCtrl>(builder: (ctrl) {
//         return SizedBox(
//           width: 260,
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               // L O G O
//               const SizedBox(height: 35),
//               DashHeader(isTablet: isTablet),
//               const SizedBox(height: 10),

//               // I T E M S
//               Expanded(
//                 child: SingleChildScrollView(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     children: [
//                       // requirements
//                       const SizedBox(height: 20),
//                       DashboardTile(
//                         icon: Icons.post_add_outlined,
//                         textName: 'Blogs',
//                         tab: isTablet,
//                         scafKey: scafKey,
//                         route: Routes.blogs,
//                         isSelected: true,
//                       ),
//                       const SizedBox(height: 5),
//                       DashboardTile(
//                         icon: Icons.image_outlined,
//                         textName: 'Gallery',
//                         tab: isTablet,
//                         scafKey: scafKey,
//                         route: Routes.gallery,
//                         isSelected: true,
//                       ),

//                       const SizedBox(height: 5),
//                     ],
//                   ),
//                 ),
//               ),
//               ElevatedButton(
//                 style: const ButtonStyle(
//                     elevation: WidgetStatePropertyAll(0),
//                     overlayColor: WidgetStatePropertyAll(
//                       Colors.transparent,
//                     ),
//                     backgroundColor:
//                         WidgetStatePropertyAll(Colors.transparent)),
//                 onPressed: () => showDialog(
//                     context: context,
//                     builder: (BuildContext context) => AlertDialog(
//                           title: const Text('Alert'),
//                           content:
//                               const Text('Are you sure you want to logout?'),
//                           actions: [
//                             TextButton(
//                                 onPressed: () async {
//                                   await FBAuth.auth.signOut();
//                                   if (context.mounted) {
//                                     context.go(Routes.signin);
//                                   }
//                                 },
//                                 child: const Text('Yes')),
//                             TextButton(
//                               onPressed: () {
//                                 Navigator.pop(context);
//                               },
//                               child: const Text('No'),
//                             )
//                             // => context.pop(), child: const Text('No')),
//                           ],
//                         )),
//                 child: const Column(
//                   mainAxisSize: MainAxisSize.min,
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Icon(CupertinoIcons.power, size: 22, color: Colors.black),
//                     Padding(
//                       padding: EdgeInsets.only(top: 5, bottom: 20),
//                       child: Text(
//                         'Logout',
//                         style: TextStyle(color: Colors.black),
//                       ),
//                     ),
//                   ],
//                 ),
//               )
//             ],
//           ),
//         );
//       }),
//     );
//   }
// }

// class DashHeader extends StatelessWidget {
//   const DashHeader({
//     super.key,
//     this.isTablet = false,
//   });
//   final bool isTablet;
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       width: double.maxFinite,
//       // height: 90,
//       clipBehavior: Clip.antiAlias,
//       decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           // const SizedBox(width: 25),
//           if (!isTablet)
//             const Expanded(
//               child: Image(
//                   image: AssetImage(
//                 'images/Luxor_logo.png',
//               )),
//             )
//         ],
//       ),
//     );
//   }
// }
