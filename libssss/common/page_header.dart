import 'package:flutter/material.dart';

class PageHeaderWithButton extends StatelessWidget {
  const PageHeaderWithButton({
    super.key,
    required this.title,
    // this.icon,
    required this.onPressed,
    this.buttonName,
    this.button = false,
  });
  final String title;
  // final IconData? icon;
  final String? buttonName;
  final VoidCallback onPressed;
  final bool button;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title,
                style: const TextStyle(
                  fontSize: 28,
                  // color: themeColor
                )),
            if (button)
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black87,
                  elevation: 0,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4)),
                  // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                ),
                onPressed: onPressed,
                // icon: Icon(
                //   icon,
                //   size: 20,
                // ),
                label: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(buttonName ?? "",
                      style: const TextStyle(fontSize: 14)),
                ),
              ),
          ],
        ),

        // Container(
        //   height: 10,
        //   width: double.maxFinite,
        //   decoration: BoxDecoration(color: Colors.grey[300]),
        // )
      ],
    );
  }
}
