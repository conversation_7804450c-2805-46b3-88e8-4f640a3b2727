import '../constants/api_endpoints.dart';
import '../constants/assets.dart';
import '../constants/colors.dart';
import '../constants/con_headers.dart';
import '../managers/network_manager/network_manager.dart';
import '../managers/storage_manager/storage_magager.dart';

late IApiEndPoints apiEndPoints;
late IStorageManager storageManager;
late IAppAssetsConstants assetsConstants;
late IColorsConstants colorsConstants;
late NetworkManager networkManager;
late IAppHeaders? iAppHeaders;

Future<void> initialBaseSetup() async {
  storageManager = IStorageManager.getInstance();
  await storageManager.init();
  assetsConstants = IAppAssetsConstants.getInstance();
  apiEndPoints = IApiEndPoints.getInstance();
  colorsConstants = IColorsConstants.getInstance();
  networkManager = NetworkManager();
  iAppHeaders = AppHeaders();
  // await sharedPreferences.loadData(config);
}
