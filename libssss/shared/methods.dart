// import 'package:flutter/material.dart';
// import 'package:foodcorp_admin/shared/firebase.dart';
// import 'package:go_router/go_router.dart';

import 'package:flutter/material.dart';

import 'firebase.dart';

bool isLoggedIn() => FBAuth.auth.currentUser != null;

final GlobalKey<ScaffoldMessengerState> snackbarKey =
    GlobalKey<ScaffoldMessengerState>();
showAppSnackBar(String message,
        {SnackBarAction? action,
        Duration duration = const Duration(milliseconds: 1500)}) =>
    snackbarKey.currentState?.showSnackBar(SnackBar(
      content: Text(
        message,
        style: const TextStyle(color: Colors.white),
      ),
      action: action,
      duration: duration,
    ));

// // class CustomSetTextField extends StatelessWidget {
// //   final String labelText;
// //   final TextEditingController controller;
// //   final Function(String)? onFieldSubmitted;
// //   // final VoidCallback onPressed;

// //   const CustomSetTextField({
// //     super.key,
// //     required this.labelText,
// //     required this.controller,
// //     this.onFieldSubmitted,
// //     // required this.onPressed,
// //   });

// //   @override
// //   Widget build(BuildContext context) {
// //     return Row(
// //       children: [
// //         Text(
// //           labelText,
// //           style: const TextStyle(fontSize: 20),
// //         ),
// //         const SizedBox(width: 18),
// //         SizedBox(
// //           width: 500,
// //           child: TextFormField(
// //             onFieldSubmitted: onFieldSubmitted,
// //             controller: controller,
// //             decoration: const InputDecoration(
// //               border: OutlineInputBorder(),
// //               hintText: 'Enter text here',
// //             ),
// //           ),
// //         ),
// //         const SizedBox(width: 12),
// //         // ElevatedButton(
// //         //   style: ButtonStyle(
// //         //       fixedSize:
// //         //       backgroundColor: WidgetStatePropertyAll(Colors.blueGrey.shade50),
// //         //       padding: const WidgetStatePropertyAll(
// //         //           EdgeInsetsDirectional.symmetric(
// //         //               horizontal: 30, vertical: 23)),
// //         //       elevation: const WidgetStatePropertyAll(0),
// //         //       shape: const WidgetStatePropertyAll(ContinuousRectangleBorder(
// //         //           borderRadius: BorderRadius.all(Radius.circular(5))))),
// //         //   onPressed: onPressed,
// //         //   child: const Text(
// //         //     'Submit',
// //         //     style: TextStyle(
// //         //         fontSize: 15, fontWeight: FontWeight.w400, color: Colors.black),
// //         //   ),
// //         // ),
// //       ],
// //     );
// //   }
// // }

// class HeaderTxt extends StatelessWidget {
//   const HeaderTxt({
//     super.key,
//     required this.txt,
//   });
//   final String txt;

//   @override
//   Widget build(BuildContext context) {
//     return Expanded(
//       child: Text(
//         txt,
//         style: const TextStyle(fontWeight: FontWeight.w600),
//       ),
//     );
//   }
// }

// class DeleteButton extends StatelessWidget {
//   const DeleteButton({
//     super.key,
//     required this.onDelete,
//   });

//   final Function onDelete;

//   @override
//   Widget build(BuildContext context) {
//     return IconButton(
//         onPressed: () async {
//           showDialog(
//             context: context,
//             builder: (context) => AlertDialog(
//               backgroundColor: Colors.white,
//               title: const Text("Delete!!"),
//               content: const Text("Are you sure you want to delete?"),
//               actions: [
//                 TextButton(
//                     onPressed: () async {
//                       onDelete();
//                     },
//                     child: const Text("Yes")),
//                 TextButton(
//                     onPressed: () async {
//                       if (context.mounted) {
//                         context.pop();
//                       }
//                     },
//                     child: const Text("No")),
//               ],
//             ),
//           );
//         },
//         icon: const Icon(Icons.delete));
//   }
// }

// class CustomSetTextfield2 extends StatelessWidget {
//   final String text;
//   final TextEditingController controller;
//   final Function(String)? onFieldSubmitted;

//   const CustomSetTextfield2(
//       {super.key,
//       required this.text,
//       required this.controller,
//       this.onFieldSubmitted});
//   @override
//   Widget build(BuildContext context) {
//     return Flex(
//       direction: Axis.horizontal,
//       children: [
//         Expanded(
//           flex: 5,
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 text,
//                 style: const TextStyle(fontSize: 15),
//               ),
//               TextFormField(
//                 decoration: const InputDecoration(
//                   border: OutlineInputBorder(),
//                   hintText: 'Enter text here',
//                 ),
//                 controller: controller,
//                 onFieldSubmitted: onFieldSubmitted,
//               ),
//               const SizedBox(height: 20)
//             ],
//           ),
//         ),
//       ],
//     );
//   }
// }
