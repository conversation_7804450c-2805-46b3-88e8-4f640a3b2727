import 'package:flutter/material.dart';

Color themeColor = Colors.grey.shade100;

const themeColorLite = Color(0xfffcedec);

final themeData = ThemeData(
    colorSchemeSeed: themeColor,
    useMaterial3: true,
    scaffoldBackgroundColor: Colors.white,
    radioTheme: const RadioThemeData(
      fillColor: WidgetStatePropertyAll(Colors.black),
    ),
    bottomSheetTheme:
        const BottomSheetThemeData(surfaceTintColor: Colors.white),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.white,
    ));
