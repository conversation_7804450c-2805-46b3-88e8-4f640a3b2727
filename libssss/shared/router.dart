// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:go_router/go_router.dart';
// import 'package:luxor_admin/shared/methods.dart';
// import 'package:luxor_admin/views/blogs/blogs_page.dart';
// import 'package:luxor_admin/views/drawer/dashboard.dart';
// import 'package:luxor_admin/views/gallery/gallery_page.dart';
// import '../controller/homectrl.dart';
// import '../views/blogs/add_edit_blog.dart';
// import '../views/loginpage.dart';

// const homeRoute = Routes.blogs;

// final GoRouter appRouter = GoRouter(
//   debugLogDiagnostics: true,
//   initialLocation: Routes.signin,
//   routes: _routes,
//   redirect: redirector,
// );

// FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
//   // routeHistory.add(state.uri.path);
//   // if (isLoggedIn() && state.fullPath == Routes.auth) {
//   //   return routeHistory.reversed.elementAt(1);
//   //   // return Routes.home;
//   // }
//   if (isLoggedIn()) {
//     if (state.fullPath == Routes.signin) {
//       if (Get.isRegistered<HomeCtrl>()) {
//         Future.delayed(const Duration(milliseconds: 10))
//             .then((value) => Get.find<HomeCtrl>().update());
//       }
//       return homeRoute;
//     } else {
//       if (Get.isRegistered<HomeCtrl>()) Get.find<HomeCtrl>().onInit();
//       return null;
//     }
//   } else {
//     return Routes.signin;
//   }
// }

// List<RouteBase> get _routes {
//   return <RouteBase>[
//     ShellRoute(
//         builder: (context, state, child) {
//           if (!Get.isRegistered<HomeCtrl>()) Get.put(HomeCtrl());
//           return DashboardScreen(
//             child: child,
//           );
//         },
//         routes: [
//           GoRoute(
//             path: Routes.blogs,
//             pageBuilder: (BuildContext context, GoRouterState state) =>
//                 const NoTransitionPage(
//               child: BlogsPage(),
//             ),
//           ),
//           GoRoute(
//             path: Routes.gallery,
//             pageBuilder: (BuildContext context, GoRouterState state) =>
//                 const NoTransitionPage(child: GalleryPage()),
//           ),
//         ]),
//     GoRoute(
//       path: Routes.signin,
//       pageBuilder: (BuildContext context, GoRouterState state) =>
//           const NoTransitionPage(
//         child: Loginpage(),
//       ),
//     ),
//     GoRoute(
//       path: Routes.addEditBlog,
//       pageBuilder: (BuildContext context, GoRouterState state) =>
//           const NoTransitionPage(
//         child: AddEditBlog(),
//       ),
//     ),
//   ];
// }

// class Routes {
//   static const signin = '/signin';
//   static const gallery = '/gallery';
//   static const blogs = '/blogs';
//   static const addEditBlog = '/addEditBlog';
// }

// // Movetoaddeditblogscreen
