import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;
  static final blogs = fb.collection('blogs');
  static final gallery = fb.collection('gallery');
}

class FBStorage {
  static final fbstore = FirebaseStorage.instance;
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}
