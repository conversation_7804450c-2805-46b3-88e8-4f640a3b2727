import 'package:flutter/material.dart';

import '../enums/enums.dart';
import '../models/blogsmodel.dart';
import 'extensions.dart';

moveToLoginScreen(
    {required BuildContext context, NavigationOperations? operation}) {
  context.operation(
    operation: operation ?? NavigationOperations.pushNamed,
    routeScreen: RouteScreens.login,
  );
}

moveToBaseModuleScreen(
    {required BuildContext context, NavigationOperations? operation}) {
  context.operation(
    operation: operation ?? NavigationOperations.pushNamed,
    routeScreen: RouteScreens.baseModule,
  );
}

moveToDashboardScreen(
    {required BuildContext context, NavigationOperations? operation}) {
  context.operation(
    operation: operation ?? NavigationOperations.pushNamed,
    routeScreen: RouteScreens.dashboard,
  );
}

moveToAddEditBlogScreen({
  required BuildContext context,
  BlogsModel? blogModel,
  List<BlogsModel>? allBlogs,
  NavigationOperations? operation,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: operation ?? NavigationOperations.pushNamed,
      routeScreen: RouteScreens.addEditBlog,
      settings: SettingArguments(
        argumentsData: {"blogModel": blogModel, "allBlogs": allBlogs},
      ),
      revertCallback: (data) {
        // handle setup if required
        print("back");

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          print("null");
        }
      });
}
