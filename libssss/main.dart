// import 'dart:ui';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:luxor_admin/firebase_options.dart';
// import 'configurations/app_configurations.dart';
// import 'shared/router.dart';
// import 'views/repositories/user_repository.dart';

// Future<void> main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//   await initialBaseSetup();
//   await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
//   runApp(const MyApp());
// }

// class MyApp extends StatelessWidget {
//   const MyApp({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return ScreenUtilInit(
//       designSize: const Size(360, 800),
//       minTextAdapt: true,
//       splitScreenMode: true,
//       builder: (_, child) {
//         return RepositoryProvider(
//           create: (context) => UserRepository(),
//           child: MaterialApp.router(
//             routerConfig: appRouter,
//             debugShowCheckedModeBanner: false,
//             theme: ThemeData(
//               primarySwatch: Colors.blue,
//               fontFamily: GoogleFonts.poppins().fontFamily,
//             ),
//             scrollBehavior: MyCustomScrollBehavior(),
//           ),
//         );
//       },
//     );
//   }
// }

// class MyCustomScrollBehavior extends MaterialScrollBehavior {
//   @override
//   Set<PointerDeviceKind> get dragDevices => {
//         PointerDeviceKind.touch,
//         PointerDeviceKind.mouse,
//         PointerDeviceKind.stylus,
//         PointerDeviceKind.unknown,
//         PointerDeviceKind.trackpad,
//       };
// }
